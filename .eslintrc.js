module.exports = {
  root: true,
  extends: '@react-native',
  plugins: ['react-native', 'react'],
  rules: {
    'react-native/no-unused-styles': 'error',
    'react-native/split-platform-components': 2,
    'react-native/no-inline-styles': 2,
    'react-native/no-color-literals': 2,
    'react-native/no-raw-text': 2,
    'react-native/no-single-element-style-arrays': 2,
    'react/jsx-sort-props': [
      'error',
      {
        shorthandFirst: true,
        ignoreCase: false,
        callbacksLast: true,
        noSortAlphabetically: false,
      },
    ],
    'react/jsx-uses-react': 'error',
    'react/jsx-uses-vars': 'error',
    'react/jsx-no-duplicate-props': 'error',
    'react/jsx-no-literals': 'warn',
    'react/no-deprecated': 'warn',
    // 'react/jsx-max-depth': ['warn', {max: 4}],
    'react/jsx-indent': ['error', 2],
    'react/jsx-first-prop-new-line': ['error', 'multiline'],
  },
};
