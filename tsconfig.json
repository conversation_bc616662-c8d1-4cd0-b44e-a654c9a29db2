{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react", "lib": ["es2015", "es2015.promise", "es2016.array.include", "dom", "ES2017"], "strict": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["src/*"]}, "noEmit": true, "resolveJsonModule": true, "target": "esnext", "types": ["jest"]}, "exclude": ["node_modules", "babel.config.js", "metro.config.js"]}