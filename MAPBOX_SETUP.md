# Hướng dẫn cấu hình Mapbox

## 1. Lấy Mapbox Access Token

1. T<PERSON><PERSON> cập [Mapbox](https://www.mapbox.com/)
2. Đ<PERSON>ng ký tài khoản hoặc đăng nhập
3. Vào **Account** → **Access tokens**
4. Tạo token mới hoặc sử dụng token mặc định
5. Copy token và thay thế trong file `.env`

## 2. Cấu hình Environment Variables

Cập nhật file `.env` với token thực của bạn:

```bash
# Mapbox Configuration
MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoieW91ci11c2VybmFtZSIsImEiOiJjbGV4YW1wbGUifQ.your_real_token_here
```

## 3. Cài đặt dependencies (đã hoàn thành)

```bash
npm install @rnmapbox/maps react-native-config react-native-permissions
```

## 4. Chạy ứng dụng

### iOS
```bash
cd ios && pod install && cd ..
npm run ios
```

### Android
```bash
npm run android
```

## 5. T<PERSON>h năng đã cấu hình

- ✅ Hiển thị bản đồ Mapbox
- ✅ Theo dõi vị trí người dùng
- ✅ Permissions cho location
- ✅ Styling với Tailwind CSS
- ✅ Utility functions cho Mapbox
- ✅ TypeScript support

## 6. Sử dụng MapView component

```tsx
import {MapView} from '@/components';

<MapView
  className="flex-1"
  showUserLocation={true}
  onLocationUpdate={(coordinates) => {
    console.log('Location:', coordinates);
  }}
  zoomLevel={15}
  initialCoordinates={[106.6297, 10.8231]} // HCM City
/>
```

## 7. Các style map có sẵn

```tsx
import {MapStyles} from '@/utils/mapbox';

// Sử dụng trong MapView
styleURL={MapStyles.STREET} // Mặc định
styleURL={MapStyles.SATELLITE}
styleURL={MapStyles.DARK}
styleURL={MapStyles.LIGHT}
```

## 8. Troubleshooting

### Lỗi "Invalid access token"
- Kiểm tra token trong file `.env`
- Đảm bảo token có quyền truy cập Maps SDK

### Lỗi location permission
- Kiểm tra permissions trong AndroidManifest.xml và Info.plist
- Chạy lại `pod install` cho iOS

### Build lỗi
- Clean build: `npm run clean` (nếu có)
- iOS: `cd ios && pod install && cd ..`
- Android: `cd android && ./gradlew clean && cd ..`
