# Hướng dẫn cấu hình Mapbox

## 1. Lấy Mapbox Access Token

1. T<PERSON><PERSON> cập [Mapbox](https://www.mapbox.com/)
2. Đ<PERSON>ng ký tài khoản hoặc đăng nhập
3. Vào **Account** → **Access tokens**
4. Tạo token mới hoặc sử dụng token mặc định
5. Copy token và thay thế trong file `.env`

## 2. Cấu hình Environment Variables

Cập nhật file `.env` với token thực của bạn:

```bash
# Mapbox Configuration
MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoieW91ci11c2VybmFtZSIsImEiOiJjbGV4YW1wbGUifQ.your_real_token_here
```

## 3. Cài đặt dependencies (đã hoàn thành)

```bash
npm install @rnmapbox/maps react-native-config react-native-permissions
```

## 4. Chạy ứng dụng

### iOS

```bash
cd ios && pod install && cd ..
npm run ios
```

### Android

```bash
npm run android
```

## 5. T<PERSON>h năng đã cấu hình

- ✅ Hiển thị bản đồ Mapbox
- ✅ Theo dõi vị trí người dùng
- ✅ Permissions cho location
- ✅ Styling với Tailwind CSS
- ✅ Utility functions cho Mapbox
- ✅ TypeScript support

## 6. Sử dụng MapView component

```tsx
import {MapView} from '@/components';

<MapView
  className="flex-1"
  showUserLocation={true}
  onLocationUpdate={coordinates => {
    console.log('Location:', coordinates);
  }}
  zoomLevel={15}
  initialCoordinates={[106.6297, 10.8231]} // HCM City
/>;
```

## 7. Các style map có sẵn

```tsx
import {MapStyles} from '@/utils/mapbox';

// Sử dụng trong MapView
styleURL={MapStyles.STREET} // Mặc định
styleURL={MapStyles.SATELLITE}
styleURL={MapStyles.DARK}
styleURL={MapStyles.LIGHT}
```

## 8. Troubleshooting

### Bản đồ không hiển thị (màn hình trắng)

1. **Kiểm tra Mapbox Access Token:**

   ```bash
   # Kiểm tra file .env
   cat .env

   # Token phải bắt đầu bằng "pk." và có độ dài khoảng 100+ ký tự
   ```

2. **Kiểm tra console logs:**

   ```bash
   # iOS
   npx react-native log-ios

   # Android
   npx react-native log-android
   ```

3. **Restart Metro và rebuild:**

   ```bash
   # Stop Metro (Ctrl+C)
   npx react-native start --reset-cache

   # Rebuild iOS
   cd ios && pod install && cd ..
   npx react-native run-ios

   # Rebuild Android
   cd android && ./gradlew clean && cd ..
   npx react-native run-android
   ```

4. **Kiểm tra network connectivity:**
   - Đảm bảo device/simulator có kết nối internet
   - Kiểm tra firewall không block Mapbox API

### Lỗi "Invalid access token"

- Kiểm tra token trong file `.env`
- Đảm bảo token có quyền truy cập Maps SDK
- Token phải được tạo từ https://account.mapbox.com/access-tokens/

### Lỗi location permission

- Kiểm tra permissions trong AndroidManifest.xml và Info.plist
- Chạy lại `pod install` cho iOS

### Build lỗi

- Clean build: `npm run clean` (nếu có)
- iOS: `cd ios && pod install && cd ..`
- Android: `cd android && ./gradlew clean && cd ..`

### Debug steps nếu map vẫn không hiển thị:

1. **Kiểm tra token có hợp lệ:**

   - Truy cập https://docs.mapbox.com/help/troubleshooting/verify-api-access-token/
   - Paste token vào để kiểm tra

2. **Test với token mới:**

   - Tạo token mới từ Mapbox account
   - Thay thế trong file .env
   - Restart app hoàn toàn

3. **Kiểm tra bundle ID/package name:**
   - iOS: Kiểm tra Bundle Identifier trong Xcode
   - Android: Kiểm tra applicationId trong build.gradle
   - Đảm bảo match với cấu hình Mapbox account
