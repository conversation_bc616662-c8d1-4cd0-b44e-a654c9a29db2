import React from 'react';
import {View, Text, TouchableOpacity, Alert} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {useRunning} from '@/contexts/RunningContext';

interface RunningControlsProps {
  className?: string;
}

const RunningControls: React.FC<RunningControlsProps> = ({className = ''}) => {
  const {state, startRun, pauseRun, resumeRun, stopRun} = useRunning();

  const handleStartRun = () => {
    startRun();
  };

  const handlePauseResume = () => {
    if (state.isPaused) {
      resumeRun();
    } else {
      pauseRun();
    }
  };

  const handleStopRun = () => {
    Alert.alert(
      'Stop Run',
      'Are you sure you want to stop this run? Your progress will be saved.',
      [
        {text: 'Cancel', style: 'cancel'},
        {text: 'Stop', style: 'destructive', onPress: stopRun},
      ],
    );
  };

  if (!state.isTracking) {
    return (
      <View className={`${className}`}>
        <TouchableOpacity
          onPress={handleStartRun}
          className="bg-gradient-to-r from-green-500 to-green-600 rounded-3xl py-8 px-8 shadow-2xl active:scale-98 border-2 border-green-400">
          <View className="items-center">
            <Icon
              name="play-circle"
              size={64}
              color="white"
              style={{marginBottom: 16}}
            />
            <Text className="text-white text-3xl font-bold mb-3">
              Start Run
            </Text>
            <Text className="text-green-100 text-lg font-medium">
              Begin your running session
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className={`${className}`}>
      <View className="flex-row space-x-6">
        {/* Pause/Resume Button */}
        <TouchableOpacity
          onPress={handlePauseResume}
          className={`flex-1 rounded-3xl py-6 px-6 shadow-2xl active:scale-98 border-2 ${
            state.isPaused
              ? 'bg-gradient-to-r from-blue-500 to-blue-600 border-blue-400'
              : 'bg-gradient-to-r from-yellow-500 to-yellow-600 border-yellow-400'
          }`}>
          <View className="items-center">
            <Icon
              name={state.isPaused ? 'play' : 'pause'}
              size={32}
              color="white"
              style={{marginBottom: 12}}
            />
            <Text className="text-white text-xl font-bold">
              {state.isPaused ? 'Resume' : 'Pause'}
            </Text>
          </View>
        </TouchableOpacity>

        {/* Stop Button */}
        <TouchableOpacity
          onPress={handleStopRun}
          className="flex-1 bg-gradient-to-r from-red-500 to-red-600 rounded-3xl py-6 px-6 shadow-2xl active:scale-98 border-2 border-red-400">
          <View className="items-center">
            <Icon
              name="stop"
              size={32}
              color="white"
              style={{marginBottom: 12}}
            />
            <Text className="text-white text-xl font-bold">Stop</Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Status Indicator */}
      <View className="mt-6 pt-4 border-t border-gray-200">
        <View className="flex-row items-center justify-center">
          <View
            className={`w-4 h-4 rounded-full mr-3 shadow-lg ${
              state.isPaused ? 'bg-yellow-500' : 'bg-green-500'
            }`}
          />
          <Text className="text-gray-700 font-semibold text-lg">
            {state.isPaused ? 'Session Paused' : 'Session Running'}
          </Text>
          {!state.isPaused && (
            <View className="ml-3">
              <Text className="text-green-500 animate-pulse text-xl">●</Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

export default RunningControls;
