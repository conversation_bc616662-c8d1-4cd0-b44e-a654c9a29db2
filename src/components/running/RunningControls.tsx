import React from 'react';
import {View, Text, TouchableOpacity, Alert} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {useRunning} from '@/contexts/RunningContext';

interface RunningControlsProps {
  className?: string;
}

const RunningControls: React.FC<RunningControlsProps> = ({className = ''}) => {
  const {state, startRun, pauseRun, resumeRun, stopRun} = useRunning();

  const handleStartRun = () => {
    startRun();
  };

  const handlePauseResume = () => {
    if (state.isPaused) {
      resumeRun();
    } else {
      pauseRun();
    }
  };

  const handleStopRun = () => {
    Alert.alert(
      'Stop Run',
      'Are you sure you want to stop this run? Your progress will be saved.',
      [
        {text: 'Cancel', style: 'cancel'},
        {text: 'Stop', style: 'destructive', onPress: stopRun},
      ],
    );
  };

  if (!state.isTracking) {
    return (
      <View className={`${className}`}>
        <TouchableOpacity
          onPress={handleStartRun}
          className="bg-blue-600 rounded-2xl py-6 px-6 shadow-lg active:scale-98 border-2 border-blue-500">
          <View className="items-center">
            <Icon
              name="play-circle"
              size={48}
              color="white"
              style={{marginBottom: 12}}
            />
            <Text className="text-white text-2xl font-bold mb-2">
              Start Run
            </Text>
            <Text className="text-blue-100 text-base">
              Begin your running session
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className={`${className}`}>
      <View className="flex-row space-x-4">
        {/* Pause/Resume Button */}
        <TouchableOpacity
          onPress={handlePauseResume}
          className={`flex-1 rounded-2xl py-4 px-4 shadow-lg active:scale-98 border-2 ${
            state.isPaused
              ? 'bg-green-600 border-green-500'
              : 'bg-orange-600 border-orange-500'
          }`}>
          <View className="items-center">
            <Icon
              name={state.isPaused ? 'play' : 'pause'}
              size={24}
              color="white"
              style={{marginBottom: 8}}
            />
            <Text className="text-white text-lg font-bold">
              {state.isPaused ? 'Resume' : 'Pause'}
            </Text>
          </View>
        </TouchableOpacity>

        {/* Stop Button */}
        <TouchableOpacity
          onPress={handleStopRun}
          className="flex-1 bg-red-600 rounded-2xl py-4 px-4 shadow-lg active:scale-98 border-2 border-red-500">
          <View className="items-center">
            <Icon
              name="stop"
              size={24}
              color="white"
              style={{marginBottom: 8}}
            />
            <Text className="text-white text-lg font-bold">Stop</Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default RunningControls;
