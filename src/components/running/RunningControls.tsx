import React from 'react';
import {View, Text, TouchableOpacity, Alert} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {useRunning} from '@/contexts/RunningContext';

interface RunningControlsProps {
  className?: string;
}

const RunningControls: React.FC<RunningControlsProps> = ({className = ''}) => {
  const {state, startRun, pauseRun, resumeRun, stopRun} = useRunning();

  const handleStartRun = () => {
    startRun();
  };

  const handlePauseResume = () => {
    if (state.isPaused) {
      resumeRun();
    } else {
      pauseRun();
    }
  };

  const handleStopRun = () => {
    Alert.alert(
      'Stop Run',
      'Are you sure you want to stop this run? Your progress will be saved.',
      [
        {text: 'Cancel', style: 'cancel'},
        {text: 'Stop', style: 'destructive', onPress: stopRun},
      ],
    );
  };

  if (!state.isTracking) {
    return (
      <View className={`${className}`}>
        <TouchableOpacity
          onPress={handleStartRun}
          className="bg-blue-600 rounded-2xl py-6 px-6 shadow-lg active:scale-98 border-2 border-blue-500">
          <View className="items-center">
            <Icon
              name="play-circle"
              size={48}
              color="white"
              style={{marginBottom: 12}}
            />
            <Text className="text-white text-2xl font-bold mb-2">
              Start Run
            </Text>
            <Text className="text-blue-100 text-base">
              Begin your running session
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className={`${className}`}>
      <View className="items-center">
        {/* Main Action Button */}
        <TouchableOpacity
          onPress={handlePauseResume}
          className={`w-20 h-20 rounded-full items-center justify-center shadow-lg active:scale-95 mb-6 ${
            state.isPaused ? 'bg-green-500' : 'bg-orange-500'
          }`}>
          <Icon
            name={state.isPaused ? 'play' : 'pause'}
            size={32}
            color="white"
          />
        </TouchableOpacity>

        {/* Stop Button */}
        <TouchableOpacity
          onPress={handleStopRun}
          className="bg-gray-100 rounded-full px-6 py-3 shadow-sm active:scale-95">
          <View className="flex-row items-center">
            <Icon
              name="stop"
              size={18}
              color="#6B7280"
              style={{marginRight: 8}}
            />
            <Text className="text-gray-600 text-base font-semibold">
              End Session
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default RunningControls;
