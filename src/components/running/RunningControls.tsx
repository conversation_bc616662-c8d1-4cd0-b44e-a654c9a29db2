import React from 'react';
import {View, Text, TouchableOpacity, Alert} from 'react-native';
import {useRunning} from '@/contexts/RunningContext';

interface RunningControlsProps {
  className?: string;
}

const RunningControls: React.FC<RunningControlsProps> = ({className = ''}) => {
  const {state, startRun, pauseRun, resumeRun, stopRun} = useRunning();

  const handleStartRun = () => {
    startRun();
  };

  const handlePauseResume = () => {
    if (state.isPaused) {
      resumeRun();
    } else {
      pauseRun();
    }
  };

  const handleStopRun = () => {
    Alert.alert(
      'Stop Run',
      'Are you sure you want to stop this run? Your progress will be saved.',
      [
        {text: 'Cancel', style: 'cancel'},
        {text: 'Stop', style: 'destructive', onPress: stopRun},
      ],
    );
  };

  if (!state.isTracking) {
    return (
      <View className={`bg-white rounded-2xl p-6 shadow-lg ${className}`}>
        <TouchableOpacity
          onPress={handleStartRun}
          className="bg-gradient-to-r from-green-500 to-green-600 rounded-2xl py-6 px-8 shadow-lg active:scale-95 transition-transform">
          <View className="items-center">
            <Text className="text-white text-2xl font-bold mb-2">Start Run</Text>
            <Text className="text-green-100 text-sm">
              🏃‍♂️ Begin your running session
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className={`bg-white rounded-2xl p-6 shadow-lg ${className}`}>
      <View className="flex-row space-x-4">
        {/* Pause/Resume Button */}
        <TouchableOpacity
          onPress={handlePauseResume}
          className={`flex-1 rounded-2xl py-4 px-6 shadow-md active:scale-95 transition-transform ${
            state.isPaused
              ? 'bg-gradient-to-r from-blue-500 to-blue-600'
              : 'bg-gradient-to-r from-yellow-500 to-yellow-600'
          }`}>
          <View className="items-center">
            <Text className="text-white text-xl font-bold mb-1">
              {state.isPaused ? 'Resume' : 'Pause'}
            </Text>
            <Text className="text-white text-opacity-80 text-xs">
              {state.isPaused ? '▶️' : '⏸️'}
            </Text>
          </View>
        </TouchableOpacity>

        {/* Stop Button */}
        <TouchableOpacity
          onPress={handleStopRun}
          className="flex-1 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl py-4 px-6 shadow-md active:scale-95 transition-transform">
          <View className="items-center">
            <Text className="text-white text-xl font-bold mb-1">Stop</Text>
            <Text className="text-red-100 text-xs">⏹️</Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Status Indicator */}
      <View className="mt-4 pt-4 border-t border-gray-100">
        <View className="flex-row items-center justify-center">
          <View
            className={`w-3 h-3 rounded-full mr-2 ${
              state.isPaused ? 'bg-yellow-500' : 'bg-green-500'
            }`}
          />
          <Text className="text-gray-600 font-medium">
            {state.isPaused ? 'Paused' : 'Running'}
          </Text>
          {!state.isPaused && (
            <View className="ml-2">
              <Text className="text-green-600 animate-pulse">●</Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

export default RunningControls;
