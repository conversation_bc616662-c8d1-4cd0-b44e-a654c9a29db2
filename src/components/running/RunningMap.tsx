import React, {useRef, useEffect} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import Mapbox, {
  MapView,
  Camera,
  LocationPuck,
  ShapeSource,
  LineLayer,
} from '@rnmapbox/maps';
import {useRunning} from '@/contexts/RunningContext';
import {Location} from '@/types/running';

interface RunningMapProps {
  className?: string;
}

const RunningMap: React.FC<RunningMapProps> = ({className = ''}) => {
  const {state} = useRunning();
  const mapRef = useRef<MapView>(null);
  const cameraRef = useRef<Camera>(null);

  // Follow user location when tracking
  useEffect(() => {
    if (state.currentLocation && state.isTracking && !state.isPaused) {
      cameraRef.current?.setCamera({
        centerCoordinate: [
          state.currentLocation.longitude,
          state.currentLocation.latitude,
        ],
        zoomLevel: 16,
        animationDuration: 1000,
      });
    }
  }, [state.currentLocation, state.isTracking, state.isPaused]);

  // Create route line from GPS points
  const createRouteGeoJSON = (route: Location[]) => {
    if (route.length < 2) return null;

    return {
      type: 'Feature',
      geometry: {
        type: 'LineString',
        coordinates: route.map(point => [point.longitude, point.latitude]),
      },
      properties: {},
    };
  };

  const routeGeoJSON = state.currentSession?.route
    ? createRouteGeoJSON(state.currentSession.route)
    : null;

  return (
    <View className={className} style={{flex: 1}}>
      <MapView
        ref={mapRef}
        style={{flex: 1}}
        styleURL={Mapbox.StyleURL.Outdoors}
        compassEnabled={true}
        compassViewPosition={3}
        compassViewMargins={{x: 16, y: 60}}
        scaleBarEnabled={false}
        logoEnabled={false}
        attributionEnabled={false}>
        <Camera
          ref={cameraRef}
          centerCoordinate={
            state.currentLocation
              ? [
                  state.currentLocation.longitude,
                  state.currentLocation.latitude,
                ]
              : [106.6297, 10.8231]
          }
          zoomLevel={16}
          animationMode="flyTo"
          animationDuration={2000}
        />

        {/* User location */}
        <LocationPuck
          puckBearingEnabled={true}
          puckBearing="heading"
          visible={true}
        />

        {/* Running route */}
        {routeGeoJSON && (
          <ShapeSource id="route-source" shape={routeGeoJSON}>
            <LineLayer
              id="route-line"
              style={{
                lineColor: '#3B82F6', // Blue color
                lineWidth: 6,
                lineOpacity: 0.8,
                lineCap: 'round',
                lineJoin: 'round',
              }}
            />
          </ShapeSource>
        )}

        {/* Start point marker */}
        {state.currentSession?.route &&
          state.currentSession.route.length > 0 && (
            <ShapeSource
              id="start-point"
              shape={{
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [
                    state.currentSession.route[0].longitude,
                    state.currentSession.route[0].latitude,
                  ],
                },
                properties: {},
              }}>
              <Mapbox.CircleLayer
                id="start-circle"
                style={{
                  circleRadius: 8,
                  circleColor: '#10B981', // Green color
                  circleStrokeColor: '#ffffff',
                  circleStrokeWidth: 2,
                }}
              />
            </ShapeSource>
          )}
      </MapView>

      {/* Map Controls */}
      <View className="absolute top-20 right-6 z-20">
        <TouchableOpacity
          className="bg-white/90 backdrop-blur rounded-2xl p-4 shadow-xl mb-4 active:scale-95"
          onPress={() => {
            if (cameraRef.current && state.currentLocation) {
              cameraRef.current.setCamera({
                centerCoordinate: [
                  state.currentLocation.longitude,
                  state.currentLocation.latitude,
                ],
                zoomLevel: 16,
                animationDuration: 1000,
              });
            }
          }}>
          <Icon name="locate" size={24} color="#3B82F6" />
        </TouchableOpacity>
      </View>

      {/* Status Overlay */}
      {state.isTracking && (
        <View className="absolute top-20 left-6 z-20">
          <View className="bg-white/95 backdrop-blur rounded-2xl px-4 py-3 shadow-xl">
            <View className="flex-row items-center">
              <View
                className={`w-3 h-3 rounded-full mr-3 ${
                  state.isPaused ? 'bg-yellow-500' : 'bg-green-500'
                }`}
              />
              <Text className="text-gray-900 text-sm font-semibold">
                {state.isPaused ? 'Paused' : 'Tracking'}
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* No Active Session Overlay */}
      {!state.currentSession && (
        <View className="absolute inset-0 bg-black/20 items-center justify-center z-10">
          <View className="bg-white/95 backdrop-blur rounded-3xl p-8 mx-8 shadow-2xl">
            <View className="items-center">
              <Icon
                name="location-outline"
                size={48}
                color="#6B7280"
                style={{marginBottom: 16}}
              />
              <Text className="text-gray-900 text-xl font-bold text-center mb-3">
                Ready to Run
              </Text>
              <Text className="text-gray-600 text-center leading-relaxed">
                Start your running session to see your route tracked on the map
                in real-time
              </Text>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

export default RunningMap;
