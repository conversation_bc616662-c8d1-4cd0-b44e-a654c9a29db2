import React, {useRef, useEffect} from 'react';
import {View, TouchableOpacity} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import Mapbox, {
  MapView,
  Camera,
  LocationPuck,
  ShapeSource,
  LineLayer,
} from '@rnmapbox/maps';
import {useRunning} from '@/contexts/RunningContext';
import {Location} from '@/types/running';

interface RunningMapProps {
  className?: string;
}

const RunningMap: React.FC<RunningMapProps> = ({className = ''}) => {
  const {state} = useRunning();
  const mapRef = useRef<MapView>(null);
  const cameraRef = useRef<Camera>(null);

  // Follow user location when tracking
  useEffect(() => {
    if (state.currentLocation && state.isTracking && !state.isPaused) {
      cameraRef.current?.setCamera({
        centerCoordinate: [
          state.currentLocation.longitude,
          state.currentLocation.latitude,
        ],
        zoomLevel: 16,
        animationDuration: 1000,
      });
    }
  }, [state.currentLocation, state.isTracking, state.isPaused]);

  // Create route line from GPS points
  const createRouteGeoJSON = (route: Location[]) => {
    if (route.length < 2) {
      return null;
    }

    return {
      type: 'Feature',
      geometry: {
        type: 'LineString',
        coordinates: route.map(point => [point.longitude, point.latitude]),
      },
      properties: {},
    };
  };

  const routeGeoJSON = state.currentSession?.route
    ? createRouteGeoJSON(state.currentSession.route)
    : null;

  return (
    <View className={className} style={{flex: 1}}>
      <MapView
        attributionEnabled={false}
        compassEnabled={true}
        compassViewMargins={{x: 16, y: 60}}
        compassViewPosition={3}
        logoEnabled={false}
        ref={mapRef}
        scaleBarEnabled={false}
        style={{flex: 1}}
        styleURL={Mapbox.StyleURL.Light}>
        <Camera
          animationDuration={2000}
          animationMode="flyTo"
          centerCoordinate={
            state.currentLocation
              ? [
                  state.currentLocation.longitude,
                  state.currentLocation.latitude,
                ]
              : [106.6297, 10.8231]
          }
          ref={cameraRef}
          zoomLevel={16}
        />

        {/* User location */}
        <LocationPuck
          puckBearing="heading"
          puckBearingEnabled={true}
          visible={true}
        />

        {/* Running route */}
        {routeGeoJSON && (
          <ShapeSource id="route-source" shape={routeGeoJSON}>
            <LineLayer
              id="route-line"
              style={{
                lineColor: '#3B82F6', // Blue color
                lineWidth: 6,
                lineOpacity: 0.8,
                lineCap: 'round',
                lineJoin: 'round',
              }}
            />
          </ShapeSource>
        )}

        {/* Start point marker */}
        {state.currentSession?.route &&
          state.currentSession.route.length > 0 && (
            <ShapeSource
              id="start-point"
              shape={{
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [
                    state.currentSession.route[0].longitude,
                    state.currentSession.route[0].latitude,
                  ],
                },
                properties: {},
              }}>
              <Mapbox.CircleLayer
                id="start-circle"
                style={{
                  circleRadius: 8,
                  circleColor: '#10B981', // Green color
                  circleStrokeColor: '#ffffff',
                  circleStrokeWidth: 2,
                }}
              />
            </ShapeSource>
          )}
      </MapView>

      {/* Map Controls */}
      <View className="absolute bottom-20 right-4 z-20">
        <TouchableOpacity
          className="bg-white/90 backdrop-blur rounded-full p-3 shadow-lg active:scale-95"
          onPress={() => {
            if (cameraRef.current && state.currentLocation) {
              cameraRef.current.setCamera({
                centerCoordinate: [
                  state.currentLocation.longitude,
                  state.currentLocation.latitude,
                ],
                zoomLevel: 16,
                animationDuration: 1000,
              });
            }
          }}>
          <Icon color="#3B82F6" name="locate" size={20} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default RunningMap;
