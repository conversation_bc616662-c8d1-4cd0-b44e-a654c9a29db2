import React, {useRef, useEffect} from 'react';
import {View, Text} from 'react-native';
import Mapbox, {
  MapView,
  Camera,
  LocationPuck,
  ShapeSource,
  LineLayer,
} from '@rnmapbox/maps';
import {useRunning} from '@/contexts/RunningContext';
import {Location} from '@/types/running';

interface RunningMapProps {
  className?: string;
}

const RunningMap: React.FC<RunningMapProps> = ({className = ''}) => {
  const {state} = useRunning();
  const mapRef = useRef<MapView>(null);
  const cameraRef = useRef<Camera>(null);

  // Follow user location when tracking
  useEffect(() => {
    if (state.currentLocation && state.isTracking && !state.isPaused) {
      cameraRef.current?.setCamera({
        centerCoordinate: [
          state.currentLocation.longitude,
          state.currentLocation.latitude,
        ],
        zoomLevel: 16,
        animationDuration: 1000,
      });
    }
  }, [state.currentLocation, state.isTracking, state.isPaused]);

  // Create route line from GPS points
  const createRouteGeoJSON = (route: Location[]) => {
    if (route.length < 2) return null;

    return {
      type: 'Feature',
      geometry: {
        type: 'LineString',
        coordinates: route.map(point => [point.longitude, point.latitude]),
      },
      properties: {},
    };
  };

  const routeGeoJSON = state.currentSession?.route
    ? createRouteGeoJSON(state.currentSession.route)
    : null;

  return (
    <View className={className} style={{flex: 1}}>
      <MapView
        ref={mapRef}
        style={{flex: 1}}
        styleURL={Mapbox.StyleURL.Outdoors}
        compassEnabled={true}
        compassViewPosition={3}
        compassViewMargins={{x: 16, y: 60}}
        scaleBarEnabled={false}
        logoEnabled={false}
        attributionEnabled={false}>
        <Camera
          ref={cameraRef}
          centerCoordinate={
            state.currentLocation
              ? [
                  state.currentLocation.longitude,
                  state.currentLocation.latitude,
                ]
              : [106.6297, 10.8231]
          }
          zoomLevel={16}
          animationMode="flyTo"
          animationDuration={2000}
        />

        {/* User location */}
        <LocationPuck
          puckBearingEnabled={true}
          puckBearing="heading"
          visible={true}
        />

        {/* Running route */}
        {routeGeoJSON && (
          <ShapeSource id="route-source" shape={routeGeoJSON}>
            <LineLayer
              id="route-line"
              style={{
                lineColor: '#3B82F6', // Blue color
                lineWidth: 6,
                lineOpacity: 0.8,
                lineCap: 'round',
                lineJoin: 'round',
              }}
            />
          </ShapeSource>
        )}

        {/* Start point marker */}
        {state.currentSession?.route &&
          state.currentSession.route.length > 0 && (
            <ShapeSource
              id="start-point"
              shape={{
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [
                    state.currentSession.route[0].longitude,
                    state.currentSession.route[0].latitude,
                  ],
                },
                properties: {},
              }}>
              <Mapbox.CircleLayer
                id="start-circle"
                style={{
                  circleRadius: 8,
                  circleColor: '#10B981', // Green color
                  circleStrokeColor: '#ffffff',
                  circleStrokeWidth: 2,
                }}
              />
            </ShapeSource>
          )}
      </MapView>

      {/* Map overlay info */}
      {state.isTracking && (
        <View className="absolute top-4 left-4 bg-black bg-opacity-70 rounded-xl px-4 py-2">
          <View className="flex-row items-center">
            <View
              className={`w-2 h-2 rounded-full mr-2 ${
                state.isPaused ? 'bg-yellow-400' : 'bg-green-400'
              }`}
            />
            <Text className="text-white text-sm font-medium">
              {state.isPaused ? 'Paused' : 'Tracking'}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

export default RunningMap;
