import React from 'react';
import {View, Text} from 'react-native';
import {RunStats} from '@/types/running';
import {formatDistance, formatDuration} from '@/utils/running';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  isUnlocked: boolean;
  progress?: number;
  target?: number;
  color: string;
}

interface AchievementCardProps {
  stats: RunStats;
  className?: string;
}

const AchievementCard: React.FC<AchievementCardProps> = ({stats, className = ''}) => {
  const achievements: Achievement[] = [
    {
      id: 'first_run',
      title: 'First Steps',
      description: 'Complete your first run',
      icon: '🏃‍♂️',
      isUnlocked: stats.totalRuns >= 1,
      color: 'bg-blue-500',
    },
    {
      id: 'five_runs',
      title: 'Getting Started',
      description: 'Complete 5 runs',
      icon: '🎯',
      isUnlocked: stats.totalRuns >= 5,
      progress: stats.totalRuns,
      target: 5,
      color: 'bg-green-500',
    },
    {
      id: 'first_5k',
      title: '5K Runner',
      description: 'Run 5 kilometers in a single session',
      icon: '🏅',
      isUnlocked: stats.longestRun >= 5000,
      color: 'bg-yellow-500',
    },
    {
      id: 'ten_hours',
      title: 'Time Warrior',
      description: 'Accumulate 10 hours of running',
      icon: '⏰',
      isUnlocked: stats.totalDuration >= 36000, // 10 hours in seconds
      progress: Math.floor(stats.totalDuration / 3600), // hours
      target: 10,
      color: 'bg-purple-500',
    },
    {
      id: 'hundred_km',
      title: 'Century Runner',
      description: 'Run a total of 100 kilometers',
      icon: '💯',
      isUnlocked: stats.totalDistance >= 100000,
      progress: Math.floor(stats.totalDistance / 1000), // km
      target: 100,
      color: 'bg-red-500',
    },
    {
      id: 'consistency',
      title: 'Consistent Runner',
      description: 'Complete 30 runs',
      icon: '🔥',
      isUnlocked: stats.totalRuns >= 30,
      progress: stats.totalRuns,
      target: 30,
      color: 'bg-orange-500',
    },
  ];

  const unlockedAchievements = achievements.filter(a => a.isUnlocked);
  const nextAchievement = achievements.find(a => !a.isUnlocked && a.progress !== undefined);

  return (
    <View className={`bg-white rounded-2xl p-6 shadow-lg ${className}`}>
      <Text className="text-xl font-bold text-gray-800 mb-4">
        🏆 Achievements
      </Text>

      {unlockedAchievements.length > 0 ? (
        <View className="mb-4">
          <Text className="text-gray-600 text-sm mb-3">
            Unlocked ({unlockedAchievements.length}/{achievements.length})
          </Text>
          <View className="flex-row flex-wrap -mx-1">
            {unlockedAchievements.slice(0, 6).map((achievement) => (
              <View key={achievement.id} className="w-1/3 px-1 mb-2">
                <View className="bg-gray-50 rounded-xl p-3 items-center">
                  <Text className="text-2xl mb-1">{achievement.icon}</Text>
                  <Text className="text-xs font-medium text-gray-700 text-center">
                    {achievement.title}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      ) : null}

      {nextAchievement ? (
        <View className="border-t border-gray-100 pt-4">
          <Text className="text-gray-600 text-sm mb-3">Next Goal</Text>
          <View className="bg-gray-50 rounded-xl p-4">
            <View className="flex-row items-center mb-2">
              <Text className="text-2xl mr-3">{nextAchievement.icon}</Text>
              <View className="flex-1">
                <Text className="font-bold text-gray-800">
                  {nextAchievement.title}
                </Text>
                <Text className="text-gray-600 text-sm">
                  {nextAchievement.description}
                </Text>
              </View>
            </View>
            
            {nextAchievement.progress !== undefined && nextAchievement.target && (
              <View className="mt-3">
                <View className="flex-row justify-between items-center mb-1">
                  <Text className="text-xs text-gray-600">Progress</Text>
                  <Text className="text-xs font-medium text-gray-800">
                    {nextAchievement.progress}/{nextAchievement.target}
                  </Text>
                </View>
                <View className="bg-gray-200 rounded-full h-2">
                  <View
                    className={`${nextAchievement.color} rounded-full h-2`}
                    style={{
                      width: `${Math.min(
                        (nextAchievement.progress / nextAchievement.target) * 100,
                        100
                      )}%`,
                    }}
                  />
                </View>
              </View>
            )}
          </View>
        </View>
      ) : (
        <View className="items-center py-4">
          <Text className="text-4xl mb-2">🎉</Text>
          <Text className="text-gray-600 text-center">
            Congratulations! You've unlocked all achievements!
          </Text>
        </View>
      )}
    </View>
  );
};

export default AchievementCard;
