import React from 'react';
import {View, Text} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {RunStats} from '@/types/running';
import {formatDistance, formatDuration} from '@/utils/running';

interface Achievement {
  id: string;
  title: string;
  description: string;
  iconName: string;
  isUnlocked: boolean;
  progress?: number;
  target?: number;
  color: string;
}

interface AchievementCardProps {
  stats: RunStats;
  className?: string;
}

const AchievementCard: React.FC<AchievementCardProps> = ({
  stats,
  className = '',
}) => {
  const achievements: Achievement[] = [
    {
      id: 'first_run',
      title: 'First Steps',
      description: 'Complete your first run',
      iconName: 'footsteps',
      isUnlocked: stats.totalRuns >= 1,
      color: 'bg-blue-500',
    },
    {
      id: 'five_runs',
      title: 'Getting Started',
      description: 'Complete 5 runs',
      iconName: 'flag',
      isUnlocked: stats.totalRuns >= 5,
      progress: stats.totalRuns,
      target: 5,
      color: 'bg-green-500',
    },
    {
      id: 'first_5k',
      title: '5K Runner',
      description: 'Run 5 kilometers in a single session',
      iconName: 'medal',
      isUnlocked: stats.longestRun >= 5000,
      color: 'bg-yellow-500',
    },
    {
      id: 'ten_hours',
      title: 'Time Warrior',
      description: 'Accumulate 10 hours of running',
      iconName: 'time',
      isUnlocked: stats.totalDuration >= 36000, // 10 hours in seconds
      progress: Math.floor(stats.totalDuration / 3600), // hours
      target: 10,
      color: 'bg-purple-500',
    },
    {
      id: 'hundred_km',
      title: 'Century Runner',
      description: 'Run a total of 100 kilometers',
      iconName: 'trophy',
      isUnlocked: stats.totalDistance >= 100000,
      progress: Math.floor(stats.totalDistance / 1000), // km
      target: 100,
      color: 'bg-red-500',
    },
    {
      id: 'consistency',
      title: 'Consistent Runner',
      description: 'Complete 30 runs',
      iconName: 'flame',
      isUnlocked: stats.totalRuns >= 30,
      progress: stats.totalRuns,
      target: 30,
      color: 'bg-orange-500',
    },
  ];

  const unlockedAchievements = achievements.filter(a => a.isUnlocked);
  const nextAchievement = achievements.find(
    a => !a.isUnlocked && a.progress !== undefined,
  );

  return (
    <View
      className={`bg-white rounded-3xl p-6 shadow-sm border border-gray-100 ${className}`}>
      <View className="flex-row items-center mb-6">
        <Icon
          color="#F59E0B"
          name="trophy"
          size={28}
          style={{marginRight: 10}}
        />
        <Text className="text-xl font-bold text-gray-900">Achievements</Text>
      </View>

      {unlockedAchievements.length > 0 ? (
        <View className="mb-4">
          <Text className="text-gray-600 text-sm mb-3">
            Unlocked ({unlockedAchievements.length}/{achievements.length})
          </Text>
          <View className="flex-row flex-wrap -mx-1">
            {unlockedAchievements.slice(0, 6).map(achievement => (
              <View className="w-1/3 px-1 mb-3 " key={achievement.id}>
                <View className="bg-green-50 rounded-2xl p-4 items-center flex-1">
                  <Icon
                    color="#10B981"
                    name={achievement.iconName}
                    size={28}
                    style={{marginBottom: 8}}
                  />
                  <Text className="text-xs font-semibold text-gray-700 text-center">
                    {achievement.title}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      ) : null}

      {nextAchievement ? (
        <View className="border-t border-gray-100 pt-6">
          <Text className="text-gray-600 text-sm mb-4 font-semibold">
            Next Goal
          </Text>
          <View className="bg-blue-50 rounded-2xl p-5">
            <View className="flex-row items-center mb-3">
              <Icon
                color="#3B82F6"
                name={nextAchievement.iconName}
                size={36}
                style={{marginRight: 12}}
              />
              <View className="flex-1">
                <Text className="font-bold text-gray-900 text-base">
                  {nextAchievement.title}
                </Text>
                <Text className="text-gray-600 text-sm">
                  {nextAchievement.description}
                </Text>
              </View>
            </View>

            {nextAchievement.progress !== undefined &&
              nextAchievement.target && (
                <View className="mt-3">
                  <View className="flex-row justify-between items-center mb-1">
                    <Text className="text-xs text-gray-600">Progress</Text>
                    <Text className="text-xs font-medium text-gray-800">
                      {nextAchievement.progress}/{nextAchievement.target}
                    </Text>
                  </View>
                  <View className="bg-gray-200 rounded-full h-2">
                    <View
                      className={`${nextAchievement.color} rounded-full h-2`}
                      style={{
                        width: `${Math.min(
                          (nextAchievement.progress / nextAchievement.target) *
                            100,
                          100,
                        )}%`,
                      }}
                    />
                  </View>
                </View>
              )}
          </View>
        </View>
      ) : (
        <View className="items-center py-4">
          <Icon
            color="#10B981"
            name="checkmark-circle"
            size={64}
            style={{marginBottom: 8}}
          />
          <Text className="text-gray-600 text-center">
            Congratulations! You've unlocked all achievements!
          </Text>
        </View>
      )}
    </View>
  );
};

export default AchievementCard;
