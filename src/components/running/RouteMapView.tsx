import React, {useEffect, useRef} from 'react';
import {View, Text, Dimensions} from 'react-native';
import MapboxGL from '@rnmapbox/maps';
import {Location} from '@/types/running';

interface RouteMapViewProps {
  route: Location[];
  className?: string;
  height?: number;
  showStartEndMarkers?: boolean;
}

const RouteMapView: React.FC<RouteMapViewProps> = ({
  route,
  className = '',
  height = 200,
  showStartEndMarkers = true,
}) => {
  const mapRef = useRef<MapboxGL.MapView>(null);
  const cameraRef = useRef<MapboxGL.Camera>(null);

  useEffect(() => {
    if (route.length > 0 && cameraRef.current) {
      // Fit the map to show the entire route
      const coordinates = route.map(point => [point.longitude, point.latitude]);
      
      if (coordinates.length === 1) {
        // Single point - center on it
        cameraRef.current.setCamera({
          centerCoordinate: coordinates[0],
          zoomLevel: 16,
          animationDuration: 1000,
        });
      } else if (coordinates.length > 1) {
        // Multiple points - fit to bounds
        cameraRef.current.fitBounds(
          [coordinates[0], coordinates[coordinates.length - 1]],
          [50, 50, 50, 50], // padding
          1000, // animation duration
        );
      }
    }
  }, [route]);

  if (route.length === 0) {
    return (
      <View 
        className={`bg-gray-100 rounded-2xl items-center justify-center ${className}`}
        style={{height}}>
        <Text className="text-gray-500 text-base">No route data available</Text>
      </View>
    );
  }

  // Convert route to GeoJSON LineString
  const routeGeoJSON = {
    type: 'Feature' as const,
    properties: {},
    geometry: {
      type: 'LineString' as const,
      coordinates: route.map(point => [point.longitude, point.latitude]),
    },
  };

  // Start and end points
  const startPoint = route[0];
  const endPoint = route[route.length - 1];

  return (
    <View className={`rounded-2xl overflow-hidden ${className}`} style={{height}}>
      <MapboxGL.MapView
        ref={mapRef}
        style={{flex: 1}}
        styleURL="mapbox://styles/mapbox/outdoors-v12"
        logoEnabled={false}
        attributionEnabled={false}
        scaleBarEnabled={false}
        compassEnabled={false}>
        
        <MapboxGL.Camera
          ref={cameraRef}
          zoomLevel={14}
          centerCoordinate={[startPoint.longitude, startPoint.latitude]}
        />

        {/* Route Line */}
        <MapboxGL.ShapeSource id="routeSource" shape={routeGeoJSON}>
          <MapboxGL.LineLayer
            id="routeLine"
            style={{
              lineColor: '#3B82F6',
              lineWidth: 4,
              lineCap: 'round',
              lineJoin: 'round',
            }}
          />
        </MapboxGL.ShapeSource>

        {/* Start Marker */}
        {showStartEndMarkers && (
          <MapboxGL.PointAnnotation
            id="startMarker"
            coordinate={[startPoint.longitude, startPoint.latitude]}>
            <View className="bg-green-500 w-4 h-4 rounded-full border-2 border-white shadow-lg" />
          </MapboxGL.PointAnnotation>
        )}

        {/* End Marker */}
        {showStartEndMarkers && route.length > 1 && (
          <MapboxGL.PointAnnotation
            id="endMarker"
            coordinate={[endPoint.longitude, endPoint.latitude]}>
            <View className="bg-red-500 w-4 h-4 rounded-full border-2 border-white shadow-lg" />
          </MapboxGL.PointAnnotation>
        )}

        {/* Route Points (optional - for detailed view) */}
        {route.length < 50 && route.map((point, index) => (
          <MapboxGL.PointAnnotation
            key={`point-${index}`}
            id={`routePoint-${index}`}
            coordinate={[point.longitude, point.latitude]}>
            <View className="bg-blue-400 w-1 h-1 rounded-full opacity-60" />
          </MapboxGL.PointAnnotation>
        ))}
      </MapboxGL.MapView>

      {/* Route Stats Overlay */}
      <View className="absolute bottom-3 left-3 right-3">
        <View className="bg-white/90 backdrop-blur rounded-xl px-3 py-2">
          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center">
              <View className="bg-green-500 w-2 h-2 rounded-full mr-2" />
              <Text className="text-xs font-medium text-gray-700">Start</Text>
            </View>
            
            <Text className="text-xs font-semibold text-gray-800">
              {route.length} GPS points
            </Text>
            
            <View className="flex-row items-center">
              <Text className="text-xs font-medium text-gray-700 mr-2">End</Text>
              <View className="bg-red-500 w-2 h-2 rounded-full" />
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default RouteMapView;
