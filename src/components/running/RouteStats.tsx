import React from 'react';
import {View, Text} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {Location} from '@/types/running';

interface RouteStatsProps {
  route: Location[];
  className?: string;
}

const RouteStats: React.FC<RouteStatsProps> = ({route, className = ''}) => {
  if (route.length === 0) {
    return null;
  }

  // Calculate route statistics
  const calculateRouteStats = () => {
    if (route.length < 2) {
      return {
        totalDistance: 0,
        averageSpeed: 0,
        maxSpeed: 0,
        elevationGain: 0,
        elevationLoss: 0,
        minElevation: 0,
        maxElevation: 0,
      };
    }

    let totalDistance = 0;
    let totalSpeed = 0;
    let maxSpeed = 0;
    let minElevation = route[0].altitude || 0;
    let maxElevation = route[0].altitude || 0;
    let elevationGain = 0;
    let elevationLoss = 0;

    for (let i = 1; i < route.length; i++) {
      const prev = route[i - 1];
      const curr = route[i];

      // Calculate distance between points using Haversine formula
      const R = 6371000; // Earth's radius in meters
      const dLat = ((curr.latitude - prev.latitude) * Math.PI) / 180;
      const dLon = ((curr.longitude - prev.longitude) * Math.PI) / 180;
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((prev.latitude * Math.PI) / 180) *
          Math.cos((curr.latitude * Math.PI) / 180) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c;

      totalDistance += distance;

      // Calculate speed if we have timestamps
      if (curr.timestamp && prev.timestamp) {
        const timeDiff = (curr.timestamp - prev.timestamp) / 1000; // seconds
        if (timeDiff > 0) {
          const speed = distance / timeDiff; // m/s
          totalSpeed += speed;
          maxSpeed = Math.max(maxSpeed, speed);
        }
      }

      // Calculate elevation changes
      if (curr.altitude !== undefined && prev.altitude !== undefined) {
        const elevationDiff = curr.altitude - prev.altitude;
        if (elevationDiff > 0) {
          elevationGain += elevationDiff;
        } else {
          elevationLoss += Math.abs(elevationDiff);
        }

        minElevation = Math.min(minElevation, curr.altitude);
        maxElevation = Math.max(maxElevation, curr.altitude);
      }
    }

    const averageSpeed = totalSpeed / (route.length - 1);

    return {
      totalDistance,
      averageSpeed,
      maxSpeed,
      elevationGain,
      elevationLoss,
      minElevation,
      maxElevation,
    };
  };

  const stats = calculateRouteStats();

  const formatDistance = (meters: number) => {
    if (meters >= 1000) {
      return `${(meters / 1000).toFixed(2)} km`;
    }
    return `${meters.toFixed(0)} m`;
  };

  const formatSpeed = (mps: number) => {
    const kmh = mps * 3.6;
    return `${kmh.toFixed(1)} km/h`;
  };

  const formatElevation = (meters: number) => {
    return `${meters.toFixed(0)} m`;
  };

  const routeStatsData = [
    {
      label: 'GPS Distance',
      value: formatDistance(stats.totalDistance),
      icon: 'resize-outline',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      iconColor: '#2563EB',
    },
    {
      label: 'Avg GPS Speed',
      value: formatSpeed(stats.averageSpeed),
      icon: 'speedometer-outline',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      iconColor: '#16A34A',
    },
    {
      label: 'Max GPS Speed',
      value: formatSpeed(stats.maxSpeed),
      icon: 'flash-outline',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      iconColor: '#EA580C',
    },
    {
      label: 'Elevation Gain',
      value: formatElevation(stats.elevationGain),
      icon: 'trending-up-outline',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      iconColor: '#9333EA',
    },
  ];

  return (
    <View
      className={`bg-white rounded-3xl p-6 shadow-sm border border-gray-100 ${className}`}>
      <View className="flex-row items-center mb-4">
        <Icon
          name="analytics"
          size={20}
          color="#6B7280"
          style={{marginRight: 8}}
        />
        <Text className="text-lg font-bold text-gray-900">Route Analysis</Text>
      </View>

      <View className="flex-row flex-wrap -mx-2">
        {routeStatsData.map((stat, index) => (
          <View key={index} className="w-1/2 px-2 mb-4">
            <View className={`${stat.bgColor} rounded-2xl p-4`}>
              <Icon
                name={stat.icon}
                size={24}
                color={stat.iconColor}
                style={{marginBottom: 8}}
              />
              <Text className={`text-lg font-bold ${stat.color} mb-1`}>
                {stat.value}
              </Text>
              <Text className="text-gray-600 text-sm font-medium">
                {stat.label}
              </Text>
            </View>
          </View>
        ))}
      </View>

      {/* Additional route info */}
      <View className="mt-4 pt-4 border-t border-gray-100">
        <View className="flex-row justify-between items-center mb-2">
          <Text className="text-gray-600 text-sm">GPS Points Recorded</Text>
          <Text className="text-gray-900 font-semibold">{route.length}</Text>
        </View>

        {stats.maxElevation > stats.minElevation && (
          <>
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-gray-600 text-sm">Elevation Range</Text>
              <Text className="text-gray-900 font-semibold">
                {formatElevation(stats.minElevation)} -{' '}
                {formatElevation(stats.maxElevation)}
              </Text>
            </View>

            <View className="flex-row justify-between items-center">
              <Text className="text-gray-600 text-sm">
                Total Elevation Loss
              </Text>
              <Text className="text-gray-900 font-semibold">
                {formatElevation(stats.elevationLoss)}
              </Text>
            </View>
          </>
        )}
      </View>
    </View>
  );
};

export default RouteStats;
