import React, {useEffect, useState} from 'react';
import {View, Text} from 'react-native';
import {useRunning} from '@/contexts/RunningContext';
import {formatDuration} from '@/utils/running';

interface RunningTimerProps {
  className?: string;
}

const RunningTimer: React.FC<RunningTimerProps> = ({className = ''}) => {
  const {state} = useRunning();
  const [currentTime, setCurrentTime] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (state.isTracking && !state.isPaused && state.currentSession) {
      interval = setInterval(() => {
        const elapsed = (Date.now() - state.currentSession!.startTime) / 1000;
        setCurrentTime(elapsed);
      }, 100); // Update every 100ms for smooth animation
    } else if (state.currentSession) {
      setCurrentTime(state.currentSession.duration);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [state.isTracking, state.isPaused, state.currentSession]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const centiseconds = Math.floor((seconds % 1) * 100);

    if (hours > 0) {
      return {
        main: `${hours.toString().padStart(2, '0')}:${minutes
          .toString()
          .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`,
        sub: `.${centiseconds.toString().padStart(2, '0')}`,
      };
    }

    return {
      main: `${minutes.toString().padStart(2, '0')}:${secs
        .toString()
        .padStart(2, '0')}`,
      sub: `.${centiseconds.toString().padStart(2, '0')}`,
    };
  };

  const timeDisplay = formatTime(currentTime);

  return (
    <View className={`items-center ${className}`}>
      <View className="bg-gray-50 rounded-3xl px-8 py-6 mb-4">
        <View className="flex-row items-baseline justify-center">
          <Text className="text-5xl font-mono font-bold text-gray-900">
            {timeDisplay.main}
          </Text>
          <Text className="text-2xl font-mono font-medium text-gray-500 ml-1">
            {timeDisplay.sub}
          </Text>
        </View>
      </View>

      <View className="flex-row items-center">
        <View
          className={`w-3 h-3 rounded-full mr-3 ${
            state.isPaused
              ? 'bg-yellow-500'
              : state.isTracking
              ? 'bg-green-500'
              : 'bg-gray-400'
          }`}
        />
        <Text className="text-gray-700 font-semibold text-base">
          {state.isPaused ? 'Paused' : state.isTracking ? 'Running' : 'Ready'}
        </Text>
      </View>
    </View>
  );
};

export default RunningTimer;
