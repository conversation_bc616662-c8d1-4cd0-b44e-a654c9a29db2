import React, {useEffect, useRef} from 'react';
import {View, Text} from 'react-native';
import MapboxGL from '@rnmapbox/maps';
import {Location} from '@/types/running';

interface MiniRouteMapProps {
  route: Location[];
  className?: string;
  height?: number;
}

const MiniRouteMap: React.FC<MiniRouteMapProps> = ({
  route,
  className = '',
  height = 120,
}) => {
  const cameraRef = useRef<MapboxGL.Camera>(null);

  useEffect(() => {
    if (route.length > 0 && cameraRef.current) {
      const coordinates = route.map(point => [point.longitude, point.latitude]);

      if (coordinates.length === 1) {
        cameraRef.current.setCamera({
          centerCoordinate: coordinates[0],
          zoomLevel: 15,
          animationDuration: 500,
        });
      } else if (coordinates.length > 1) {
        // Calculate bounds
        const lats = coordinates.map(coord => coord[1]);
        const lngs = coordinates.map(coord => coord[0]);

        const minLat = Math.min(...lats);
        const maxLat = Math.max(...lats);
        const minLng = Math.min(...lngs);
        const maxLng = Math.max(...lngs);

        cameraRef.current.fitBounds(
          [
            [minLng, minLat],
            [maxLng, maxLat],
          ],
          [20, 20, 20, 20], // smaller padding for mini map
          500,
        );
      }
    }
  }, [route]);

  if (route.length === 0) {
    return (
      <View
        className={`bg-gray-100 rounded-xl items-center justify-center ${className}`}
        style={{height}}>
        <Text className="text-gray-400 text-xs">No route</Text>
      </View>
    );
  }

  // Convert route to GeoJSON LineString
  const routeGeoJSON = {
    type: 'Feature' as const,
    properties: {},
    geometry: {
      type: 'LineString' as const,
      coordinates: route.map(point => [point.longitude, point.latitude]),
    },
  };

  const startPoint = route[0];
  const endPoint = route[route.length - 1];

  return (
    <View
      className={`rounded-xl overflow-hidden ${className}`}
      style={{height}}>
      <MapboxGL.MapView
        attributionEnabled={false}
        compassEnabled={false}
        logoEnabled={false}
        pitchEnabled={false}
        rotateEnabled={false}
        scaleBarEnabled={false}
        scrollEnabled={false}
        style={{flex: 1}}
        styleURL="mapbox://styles/mapbox/light-v11"
        zoomEnabled={false}>
        <MapboxGL.Camera
          centerCoordinate={[startPoint.longitude, startPoint.latitude]}
          ref={cameraRef}
          zoomLevel={13}
        />

        {/* Route Line */}
        <MapboxGL.ShapeSource id="miniRouteSource" shape={routeGeoJSON}>
          <MapboxGL.LineLayer
            id="miniRouteLine"
            style={{
              lineColor: '#3B82F6',
              lineWidth: 3,
              lineCap: 'round',
              lineJoin: 'round',
            }}
          />
        </MapboxGL.ShapeSource>

        {/* Start Marker */}
        <MapboxGL.PointAnnotation
          coordinate={[startPoint.longitude, startPoint.latitude]}
          id="miniStartMarker">
          <View className="bg-green-500 w-3 h-3 rounded-full border border-white" />
        </MapboxGL.PointAnnotation>

        {/* End Marker */}
        {route.length > 1 && (
          <MapboxGL.PointAnnotation
            coordinate={[endPoint.longitude, endPoint.latitude]}
            id="miniEndMarker">
            <View className="bg-red-500 w-3 h-3 rounded-full border border-white" />
          </MapboxGL.PointAnnotation>
        )}
      </MapboxGL.MapView>

      {/* Overlay gradient for better text visibility */}
      <View className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none" />

      {/* Route info overlay */}
      <View className="absolute bottom-2 right-2">
        <View className="bg-white/90 backdrop-blur rounded-lg px-2 py-1">
          <Text className="text-xs font-semibold text-gray-800">
            {route.length} pts
          </Text>
        </View>
      </View>
    </View>
  );
};

export default MiniRouteMap;
