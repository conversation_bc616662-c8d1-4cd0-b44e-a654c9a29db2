import React from 'react';
import {View, Text} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {RunSession} from '@/types/running';
import {formatDistance, formatPace, formatSpeed} from '@/utils/running';
import RunningTimer from './RunningTimer';

interface RunningStatsProps {
  session: RunSession | null;
  className?: string;
}

const RunningStats: React.FC<RunningStatsProps> = ({
  session,
  className = '',
}) => {
  if (!session) {
    return (
      <View className={`bg-white rounded-2xl p-6 shadow-lg ${className}`}>
        <Text className="text-gray-500 text-center">No active session</Text>
      </View>
    );
  }

  const stats = [
    {
      label: 'Distance',
      value: formatDistance(session.distance),
      iconName: 'map-outline',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      iconColor: '#2563EB',
    },
    {
      label: 'Calories',
      value: `${session.calories} kcal`,
      iconName: 'flame-outline',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      iconColor: '#DC2626',
    },
    {
      label: 'Pace',
      value: formatPace(session.averagePace),
      iconName: 'flash-outline',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      iconColor: '#EA580C',
    },
    {
      label: 'Speed',
      value: formatSpeed(session.averageSpeed),
      iconName: 'speedometer-outline',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      iconColor: '#9333EA',
    },
  ];

  return (
    <View className={`bg-white rounded-2xl p-6 shadow-lg ${className}`}>
      {/* Timer Display */}
      <RunningTimer className="mb-6" />

      <View className="grid grid-cols-2 gap-4">
        {stats.map((stat, index) => (
          <View
            key={index}
            className={`${stat.bgColor} rounded-xl p-4 items-center`}>
            <Icon
              name={stat.iconName}
              size={32}
              color={stat.iconColor}
              style={{marginBottom: 8}}
            />
            <Text className={`text-2xl font-bold ${stat.color} mb-1`}>
              {stat.value}
            </Text>
            <Text className="text-gray-600 text-sm font-medium">
              {stat.label}
            </Text>
          </View>
        ))}
      </View>

      {/* Additional metrics */}
      <View className="mt-4 pt-4 border-t border-gray-100">
        <View className="flex-row justify-between items-center">
          <View className="items-center flex-1">
            <Text className="text-lg font-bold text-red-600">
              {session.calories}
            </Text>
            <Text className="text-gray-600 text-xs">Calories</Text>
          </View>

          <View className="items-center flex-1">
            <Text className="text-lg font-bold text-indigo-600">
              {formatSpeed(session.maxSpeed)}
            </Text>
            <Text className="text-gray-600 text-xs">Max Speed</Text>
          </View>

          <View className="items-center flex-1">
            <Text className="text-lg font-bold text-teal-600">
              {session.route.length}
            </Text>
            <Text className="text-gray-600 text-xs">GPS Points</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default RunningStats;
