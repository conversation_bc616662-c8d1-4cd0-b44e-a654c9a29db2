import React from 'react';
import {View, Text} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {RunSession} from '@/types/running';

interface QuickStatsProps {
  session: RunSession;
  className?: string;
}

const QuickStats: React.FC<QuickStatsProps> = ({session, className = ''}) => {
  const statsData = [
    {
      icon: 'speedometer-outline',
      value: `${(session.averageSpeed * 3.6).toFixed(1)} km/h`,
      label: 'Avg Speed',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      iconColor: '#2563EB',
    },
    {
      icon: 'location-outline',
      value: session.route.length.toString(),
      label: 'GPS Points',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      iconColor: '#16A34A',
    },
    {
      icon: 'trending-up-outline',
      value: `${(session.maxSpeed * 3.6).toFixed(1)} km/h`,
      label: 'Max Speed',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      iconColor: '#EA580C',
    },
    {
      icon: 'heart-outline',
      value: `${session.calories}`,
      label: 'Calories',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      iconColor: '#DC2626',
    },
  ];

  return (
    <View className={`bg-white/95 backdrop-blur rounded-3xl p-5 shadow-xl ${className}`}>
      <View className="flex-row items-center mb-4">
        <Icon name="analytics" size={20} color="#6B7280" style={{marginRight: 8}} />
        <Text className="text-lg font-bold text-gray-900">Live Stats</Text>
      </View>
      
      <View className="flex-row flex-wrap -mx-1">
        {statsData.map((stat, index) => (
          <View key={index} className="w-1/2 px-1 mb-3">
            <View className={`${stat.bgColor} rounded-2xl p-4 items-center`}>
              <Icon
                name={stat.icon}
                size={24}
                color={stat.iconColor}
                style={{marginBottom: 8}}
              />
              <Text className={`text-lg font-bold ${stat.color} mb-1`}>
                {stat.value}
              </Text>
              <Text className="text-gray-600 text-xs font-medium text-center">
                {stat.label}
              </Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default QuickStats;
