import React, {useState} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import Mapbox, {MapView, Camera} from '@rnmapbox/maps';
import Config from 'react-native-config';

// Cấu hình Mapbox access token
const accessToken = Config.MAPBOX_ACCESS_TOKEN || '';
console.log(
  'SimpleMapView - Access Token:',
  accessToken ? 'Token loaded' : 'No token found',
);
console.log('SimpleMapView - Token length:', accessToken.length);

if (accessToken) {
  Mapbox.setAccessToken(accessToken);
} else {
  console.error('No Mapbox access token found!');
}

const SimpleMapView: React.FC = () => {
  const [currentStyle, setCurrentStyle] = useState(
    'mapbox://styles/mapbox/streets-v12',
  );

  const styles = [
    'mapbox://styles/mapbox/streets-v12',
    'mapbox://styles/mapbox/outdoors-v12',
    'mapbox://styles/mapbox/light-v11',
    'mapbox://styles/mapbox/dark-v11',
    'mapbox://styles/mapbox/satellite-v9',
  ];

  const handleMapReady = () => {
    console.log('SimpleMapView - Map is ready');
  };

  const handleMapError = () => {
    console.error('SimpleMapView - Map loading error');
  };

  if (!accessToken) {
    return (
      <View className="flex-1 items-center justify-center bg-gray-100">
        <Text className="text-red-500 text-center px-4">
          Mapbox Access Token not found.{'\n'}
          Please check your .env file.
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1">
      <MapView
        style={{flex: 1}}
        styleURL={currentStyle}
        onDidFailLoadingMap={error =>
          console.error('Map failed to load:', error)
        }
        onDidFinishLoadingMap={handleMapReady}
        onDidFinishLoadingStyle={() =>
          console.log('Style loaded:', currentStyle)
        }
        onMapLoadingError={handleMapError}>
        <Camera
          animationDuration={2000}
          animationMode="flyTo"
          centerCoordinate={[106.6297, 10.8231]} // Ho Chi Minh City
          zoomLevel={10}
        />
      </MapView>

      {/* Style Switcher */}
      <View className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-2">
        <Text className="text-xs font-semibold mb-2 text-gray-700">
          Map Style
        </Text>
        {styles.map((style, index) => (
          <TouchableOpacity
            className="px-3 py-2 rounded-md"
            key={style}
            onPress={() => {
              console.log('Switching to style:', style);
              setCurrentStyle(style);
            }}>
            <Text
              className={`text-sm ${
                currentStyle === style
                  ? 'text-blue-600 font-semibold'
                  : 'text-gray-600'
              }`}>
              {['Streets', 'Outdoors', 'Light', 'Dark', 'Satellite'][index]}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

export default SimpleMapView;
