import React from 'react';
import {View, Text} from 'react-native';
import Mapbox, {MapView, Camera} from '@rnmapbox/maps';
import Config from 'react-native-config';

// Cấu hình Mapbox access token
const accessToken = Config.MAPBOX_ACCESS_TOKEN || '';
console.log('SimpleMapView - Access Token:', accessToken ? 'Token loaded' : 'No token found');
console.log('SimpleMapView - Token length:', accessToken.length);

if (accessToken) {
  Mapbox.setAccessToken(accessToken);
} else {
  console.error('No Mapbox access token found!');
}

const SimpleMapView: React.FC = () => {
  const handleMapReady = () => {
    console.log('SimpleMapView - Map is ready');
  };

  const handleMapError = () => {
    console.error('SimpleMapView - Map loading error');
  };

  if (!accessToken) {
    return (
      <View className="flex-1 items-center justify-center bg-gray-100">
        <Text className="text-red-500 text-center px-4">
          Mapbox Access Token not found.{'\n'}
          Please check your .env file.
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1">
      <MapView
        className="flex-1"
        styleURL={Mapbox.StyleURL.Street}
        onDidFinishLoadingMap={handleMapReady}
        onMapLoadingError={handleMapError}>
        <Camera
          centerCoordinate={[106.6297, 10.8231]} // Ho Chi Minh City
          zoomLevel={10}
          animationMode="flyTo"
          animationDuration={2000}
        />
      </MapView>
    </View>
  );
};

export default SimpleMapView;
