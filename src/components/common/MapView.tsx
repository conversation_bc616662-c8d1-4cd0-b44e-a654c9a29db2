import React, {useRef, useEffect, useState} from 'react';
import {View, Alert, Platform, TouchableOpacity, Text} from 'react-native';
import Mapbox, {
  MapView,
  Camera,
  LocationPuck,
  PointAnnotation,
} from '@rnmapbox/maps';
import {request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import Config from 'react-native-config';
import {MapStyles} from '@/utils/mapbox';

// Cấu hình Mapbox access token
const accessToken = Config.MAPBOX_ACCESS_TOKEN || '';
console.log(
  'Mapbox Access Token:',
  accessToken ? 'Token loaded' : 'No token found',
);
Mapbox.setAccessToken(accessToken);

interface MapViewComponentProps {
  className?: string;
  initialCoordinates?: [number, number]; // [longitude, latitude]
  zoomLevel?: number;
  showUserLocation?: boolean;
  onLocationUpdate?: (coordinates: [number, number]) => void;
  markers?: Array<{
    id: string;
    coordinates: [number, number];
    title?: string;
    description?: string;
  }>;
  mapStyle?: keyof typeof MapStyles;
  showStyleSwitcher?: boolean;
}

const MapViewComponent: React.FC<MapViewComponentProps> = ({
  className = 'flex-1',
  initialCoordinates = [106.6297, 10.8231], // Ho Chi Minh City coordinates
  zoomLevel = 10,
  showUserLocation = true,
  onLocationUpdate,
  markers = [],
  mapStyle = 'STREET',
  showStyleSwitcher = false,
}) => {
  const mapRef = useRef<MapView>(null);
  const cameraRef = useRef<Camera>(null);
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  const [currentMapStyle, setCurrentMapStyle] = useState(mapStyle);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const permission = Platform.select({
        ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
        android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      });

      if (!permission) {
        return;
      }

      const result = await request(permission);

      if (result === RESULTS.GRANTED) {
        setHasLocationPermission(true);
      } else {
        Alert.alert(
          'Location Permission',
          'Location permission is required to show your position on the map.',
          [
            {text: 'Cancel', style: 'cancel'},
            {
              text: 'Settings',
              onPress: () => {
                /* Open settings */
              },
            },
          ],
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
    }
  };

  const handleUserLocationUpdate = (location: any) => {
    console.log('Location update received:', location);
    if (location?.coords && onLocationUpdate) {
      const coordinates: [number, number] = [
        location.coords.longitude,
        location.coords.latitude,
      ];
      onLocationUpdate(coordinates);
    }
  };

  const handleMapReady = () => {
    console.log('Map is ready');
  };

  const handleMapError = () => {
    console.error('Map loading error occurred');
  };

  return (
    <View className={className}>
      <MapView
        attributionEnabled={false}
        className="flex-1"
        compassEnabled={true}
        compassViewMargins={{x: 16, y: 60}}
        compassViewPosition={3} // Top right
        logoEnabled={false}
        ref={mapRef}
        scaleBarEnabled={false}
        styleURL={MapStyles[currentMapStyle]}
        onDidFinishLoadingMap={handleMapReady}
        onMapLoadingError={handleMapError}
        onUserLocationUpdate={handleUserLocationUpdate}>
        <Camera
          animationDuration={2000}
          animationMode="flyTo"
          centerCoordinate={initialCoordinates}
          ref={cameraRef}
          zoomLevel={zoomLevel}
        />

        {showUserLocation && hasLocationPermission && (
          <LocationPuck
            puckBearing="heading"
            puckBearingEnabled={true}
            visible={true}
          />
        )}

        {/* Render markers */}
        {markers.map(marker => (
          <PointAnnotation
            coordinate={marker.coordinates}
            id={marker.id}
            key={marker.id}>
            <View className="bg-primary rounded-full w-6 h-6 items-center justify-center">
              <View className="bg-white rounded-full w-3 h-3" />
            </View>
          </PointAnnotation>
        ))}
      </MapView>

      {/* Style Switcher */}
      {showStyleSwitcher && (
        <View className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-2">
          <TouchableOpacity
            className="px-3 py-2 rounded-md"
            onPress={() => setCurrentMapStyle('STREET')}>
            <Text
              className={`text-sm ${
                currentMapStyle === 'STREET'
                  ? 'text-primary font-semibold'
                  : 'text-gray-600'
              }`}>
              Street
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="px-3 py-2 rounded-md"
            onPress={() => setCurrentMapStyle('SATELLITE')}>
            <Text
              className={`text-sm ${
                currentMapStyle === 'SATELLITE'
                  ? 'text-primary font-semibold'
                  : 'text-gray-600'
              }`}>
              Satellite
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="px-3 py-2 rounded-md"
            onPress={() => setCurrentMapStyle('DARK')}>
            <Text
              className={`text-sm ${
                currentMapStyle === 'DARK'
                  ? 'text-primary font-semibold'
                  : 'text-gray-600'
              }`}>
              Dark
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default MapViewComponent;
