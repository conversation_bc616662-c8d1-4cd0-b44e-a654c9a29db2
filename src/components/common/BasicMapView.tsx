import React from 'react';
import {View, Text} from 'react-native';
import Mapbox, {MapView, Camera, ShapeSource, FillLayer} from '@rnmapbox/maps';
import Config from 'react-native-config';

// Cấu hình Mapbox access token
const accessToken = Config.MAPBOX_ACCESS_TOKEN || '';
console.log(
  'BasicMapView - Access Token:',
  accessToken ? 'Token loaded' : 'No token found',
);

if (accessToken) {
  Mapbox.setAccessToken(accessToken);
} else {
  console.error('No Mapbox access token found!');
}

// Tạo một GeoJSON đơn giản để test
const testGeoJSON = {
  type: 'Feature',
  geometry: {
    type: 'Polygon',
    coordinates: [
      [
        [106.6, 10.8],
        [106.7, 10.8],
        [106.7, 10.9],
        [106.6, 10.9],
        [106.6, 10.8],
      ],
    ],
  },
};

const BasicMapView: React.FC = () => {
  const handleMapReady = () => {
    console.log('BasicMapView - Map is ready');
  };

  const handleMapError = () => {
    console.error('BasicMapView - Map loading error');
  };

  if (!accessToken) {
    return (
      <View className="flex-1 items-center justify-center bg-gray-100">
        <Text className="text-red-500 text-center px-4">
          Mapbox Access Token not found.{'\n'}
          Please check your .env file.
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1">
      <MapView
        attributionEnabled={true}
        logoEnabled={true}
        style={{flex: 1}}
        styleURL={Mapbox.StyleURL.Light}
        onDidFailLoadingMap={error =>
          console.error('BasicMapView - Map failed:', error)
        }
        onDidFinishLoadingMap={handleMapReady}
        onDidFinishLoadingStyle={() =>
          console.log('BasicMapView - Style loaded')
        }
        onMapLoadingError={handleMapError}>
        <Camera
          animationDuration={1000}
          animationMode="flyTo"
          centerCoordinate={[106.6297, 10.8231]}
          zoomLevel={12}
        />

        {/* Test shape để đảm bảo map hoạt động */}
        <ShapeSource id="test-source" shape={testGeoJSON}>
          <FillLayer
            id="test-fill"
            style={{
              fillColor: '#ff0000',
              fillOpacity: 0.3,
            }}
          />
        </ShapeSource>
      </MapView>

      <View className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3">
        <Text className="text-xs text-gray-600">Basic Map Test</Text>
        <Text className="text-xs text-gray-500">Red square should appear</Text>
      </View>
    </View>
  );
};

export default BasicMapView;
