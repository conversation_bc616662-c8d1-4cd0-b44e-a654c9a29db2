import {StyleSheet, Text, View, SafeAreaView} from 'react-native';
import React, {useState} from 'react';
import {MapView} from '@/components';

type Props = {};

const Home = (props: Props) => {
  const [userLocation, setUserLocation] = useState<[number, number] | null>(
    null,
  );

  const handleLocationUpdate = (coordinates: [number, number]) => {
    setUserLocation(coordinates);
    console.log('User location updated:', coordinates);
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="bg-primary px-4 py-3 shadow-sm">
        <Text className="text-white text-lg font-semibold text-center">
          Mapbox Demo
        </Text>
        {userLocation && (
          <Text className="text-white/80 text-sm text-center mt-1">
            Lat: {userLocation[1].toFixed(6)}, Lng: {userLocation[0].toFixed(6)}
          </Text>
        )}
      </View>

      {/* Map Container */}
      <View className="flex-1">
        <MapView
          className="flex-1"
          showUserLocation={true}
          zoomLevel={15}
          onLocationUpdate={handleLocationUpdate}
        />
      </View>

      {/* Bottom Info Panel */}
      <View className="bg-white border-t border-gray-200 px-4 py-3">
        <Text className="text-gray-600 text-sm text-center">
          🗺️ Mapbox integration với React Native & Tailwind CSS
        </Text>
      </View>
    </SafeAreaView>
  );
};

export default Home;

const styles = StyleSheet.create({});
