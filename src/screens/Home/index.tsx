import {Text, View, SafeAreaView} from 'react-native';
import React from 'react';
import SimpleMapView from '@/components/common/SimpleMapView';

const Home = () => {
  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="bg-primary px-4 py-3 shadow-sm">
        <Text className="text-white text-lg font-semibold text-center">
          Mapbox Demo
        </Text>
      </View>

      {/* Map Container */}
      <View className="flex-1">
        <SimpleMapView />
      </View>

      {/* Bottom Info Panel */}
      <View className="bg-white border-t border-gray-200 px-4 py-3">
        <Text className="text-gray-600 text-sm text-center">
          🗺️ Mapbox integration với React Native & Tailwind CSS
        </Text>
      </View>
    </SafeAreaView>
  );
};

export default Home;
