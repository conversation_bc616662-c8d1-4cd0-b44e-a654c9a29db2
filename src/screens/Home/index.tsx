import {Text, View, SafeAreaView, TouchableOpacity} from 'react-native';
import React, {useState} from 'react';
import SimpleMapView from '@/components/common/SimpleMapView';
import BasicMapView from '@/components/common/BasicMapView';

const Home = () => {
  const [mapType, setMapType] = useState<'simple' | 'basic'>('basic');

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="bg-primary px-4 py-3 shadow-sm">
        <Text className="text-white text-lg font-semibold text-center">
          Mapbox Demo
        </Text>
        <View className="flex-row justify-center mt-2 space-x-2">
          <TouchableOpacity
            className={`px-3 py-1 rounded ${
              mapType === 'basic' ? 'bg-white' : 'bg-primary-dark'
            }`}
            onPress={() => setMapType('basic')}>
            <Text
              className={`text-xs ${
                mapType === 'basic' ? 'text-primary' : 'text-white'
              }`}>
              Basic
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            className={`px-3 py-1 rounded ${
              mapType === 'simple' ? 'bg-white' : 'bg-primary-dark'
            }`}
            onPress={() => setMapType('simple')}>
            <Text
              className={`text-xs ${
                mapType === 'simple' ? 'text-primary' : 'text-white'
              }`}>
              Simple
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Map Container */}
      <View className="flex-1">
        {mapType === 'basic' ? <BasicMapView /> : <SimpleMapView />}
      </View>

      {/* Bottom Info Panel */}
      <View className="bg-white border-t border-gray-200 px-4 py-3">
        <Text className="text-gray-600 text-sm text-center">
          🗺️ Mapbox integration với React Native & Tailwind CSS
        </Text>
      </View>
    </SafeAreaView>
  );
};

export default Home;
