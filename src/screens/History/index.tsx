import React, {useState} from 'react';
import {
  View,
  Text,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {useRunning} from '@/contexts/RunningContext';
import {formatDistance, formatDuration, formatPace} from '@/utils/running';
import {RunSession} from '@/types/running';

const History: React.FC = () => {
  const {state} = useRunning();
  const [selectedRun, setSelectedRun] = useState<RunSession | null>(null);

  const renderRunItem = ({item, index}: {item: RunSession; index: number}) => (
    <TouchableOpacity
      className="bg-white rounded-2xl p-4 mb-3 shadow-lg active:scale-98"
      onPress={() => setSelectedRun(item)}>
      <View className="flex-row justify-between items-start mb-3">
        <View>
          <Text className="text-lg font-bold text-gray-800">
            Run #{state.runHistory.length - index}
          </Text>
          <Text className="text-gray-500 text-sm">
            {new Date(item.startTime).toLocaleDateString('en-US', {
              weekday: 'short',
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            })}
          </Text>
        </View>
        <View className="bg-blue-50 px-3 py-1 rounded-full">
          <Text className="text-blue-600 text-xs font-semibold">
            {new Date(item.startTime).toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </Text>
        </View>
      </View>

      <View className="flex-row justify-between">
        <View className="items-center">
          <Text className="text-blue-600 font-bold text-lg">
            {formatDistance(item.distance)}
          </Text>
          <Text className="text-gray-500 text-xs">Distance</Text>
        </View>

        <View className="items-center">
          <Text className="text-green-600 font-bold text-lg">
            {formatDuration(item.duration)}
          </Text>
          <Text className="text-gray-500 text-xs">Duration</Text>
        </View>

        <View className="items-center">
          <Text className="text-orange-600 font-bold text-lg">
            {formatPace(item.averagePace)}
          </Text>
          <Text className="text-gray-500 text-xs">Pace</Text>
        </View>

        <View className="items-center">
          <Text className="text-red-600 font-bold text-lg">
            {item.calories}
          </Text>
          <Text className="text-gray-500 text-xs">kcal</Text>
        </View>
      </View>

      {item.route.length > 0 && (
        <View className="mt-3 pt-3 border-t border-gray-100 flex-row items-center">
          <Icon
            name="location"
            size={12}
            color="#6B7280"
            style={{marginRight: 4}}
          />
          <Text className="text-gray-600 text-xs">
            {item.route.length} GPS points recorded
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View className="flex-1 items-center justify-center px-8">
      <Icon
        name="footsteps-outline"
        size={80}
        color="#9CA3AF"
        style={{marginBottom: 24}}
      />
      <Text className="text-2xl font-bold text-gray-800 mb-4 text-center">
        No Runs Yet
      </Text>
      <Text className="text-gray-600 text-center mb-8">
        Start your first run to see your running history here. Every step
        counts!
      </Text>
      <TouchableOpacity className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl px-8 py-4 flex-row items-center">
        <Icon
          name="play-circle"
          size={24}
          color="white"
          style={{marginRight: 8}}
        />
        <Text className="text-white font-bold text-lg">
          Start Your First Run
        </Text>
      </TouchableOpacity>
    </View>
  );

  if (selectedRun) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50">
        <View className="flex-row items-center justify-between px-4 py-4 bg-white shadow-sm">
          <TouchableOpacity
            className="bg-gray-100 rounded-full p-2 flex-row items-center"
            onPress={() => setSelectedRun(null)}>
            <Icon
              name="arrow-back"
              size={20}
              color="#6B7280"
              style={{marginRight: 4}}
            />
            <Text className="text-gray-600 font-bold">Back</Text>
          </TouchableOpacity>
          <Text className="text-lg font-bold text-gray-800">Run Details</Text>
          <View className="w-16" />
        </View>

        <ScrollView className="flex-1 px-4 py-6">
          {/* Run Overview */}
          <View className="bg-white rounded-2xl p-6 mb-6 shadow-lg">
            <Text className="text-2xl font-bold text-gray-800 mb-2">
              Run #
              {state.runHistory.findIndex(r => r.id === selectedRun.id) + 1}
            </Text>
            <Text className="text-gray-600 mb-4">
              {new Date(selectedRun.startTime).toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'long',
                day: 'numeric',
                year: 'numeric',
              })}{' '}
              at {new Date(selectedRun.startTime).toLocaleTimeString()}
            </Text>

            <View className="grid grid-cols-2 gap-4">
              <View className="bg-blue-50 rounded-xl p-4">
                <Text className="text-3xl font-bold text-blue-600 mb-1">
                  {formatDistance(selectedRun.distance)}
                </Text>
                <Text className="text-gray-600 text-sm">Total Distance</Text>
              </View>

              <View className="bg-green-50 rounded-xl p-4">
                <Text className="text-3xl font-bold text-green-600 mb-1">
                  {formatDuration(selectedRun.duration)}
                </Text>
                <Text className="text-gray-600 text-sm">Duration</Text>
              </View>

              <View className="bg-orange-50 rounded-xl p-4">
                <Text className="text-3xl font-bold text-orange-600 mb-1">
                  {formatPace(selectedRun.averagePace)}
                </Text>
                <Text className="text-gray-600 text-sm">Average Pace</Text>
              </View>

              <View className="bg-red-50 rounded-xl p-4">
                <Text className="text-3xl font-bold text-red-600 mb-1">
                  {selectedRun.calories}
                </Text>
                <Text className="text-gray-600 text-sm">Calories Burned</Text>
              </View>
            </View>
          </View>

          {/* Additional Stats */}
          <View className="bg-white rounded-2xl p-6 shadow-lg">
            <Text className="text-xl font-bold text-gray-800 mb-4">
              Performance Details
            </Text>

            <View className="space-y-4">
              <View className="flex-row justify-between items-center">
                <Text className="text-gray-600">Max Speed</Text>
                <Text className="font-bold text-gray-800">
                  {(selectedRun.maxSpeed * 3.6).toFixed(1)} km/h
                </Text>
              </View>

              <View className="flex-row justify-between items-center">
                <Text className="text-gray-600">GPS Points</Text>
                <Text className="font-bold text-gray-800">
                  {selectedRun.route.length} points
                </Text>
              </View>

              <View className="flex-row justify-between items-center">
                <Text className="text-gray-600">Start Time</Text>
                <Text className="font-bold text-gray-800">
                  {new Date(selectedRun.startTime).toLocaleTimeString()}
                </Text>
              </View>

              {selectedRun.endTime && (
                <View className="flex-row justify-between items-center">
                  <Text className="text-gray-600">End Time</Text>
                  <Text className="font-bold text-gray-800">
                    {new Date(selectedRun.endTime).toLocaleTimeString()}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-white px-4 py-6 shadow-sm">
        <Text className="text-3xl font-bold text-gray-800 mb-2">
          Running History
        </Text>
        <Text className="text-gray-600">
          {state.runHistory.length} runs completed
        </Text>
      </View>

      {/* Run List */}
      {state.runHistory.length > 0 ? (
        <FlatList
          contentContainerStyle={{padding: 16}}
          data={state.runHistory}
          keyExtractor={item => item.id}
          renderItem={renderRunItem}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        renderEmptyState()
      )}
    </SafeAreaView>
  );
};

export default History;
