import React, {useEffect, useState} from 'react';
import {
  View,
  SafeAreaView,
  Alert,
  AppState,
  Platform,
  Text,
} from 'react-native';
import {request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import Geolocation from '@react-native-community/geolocation';
import {useRunning} from '@/contexts/RunningContext';
import RunningControls from '@/components/running/RunningControls';
import RunningMap from '@/components/running/RunningMap';
import RunningTimer from '@/components/running/RunningTimer';
import {formatDistance, formatPace} from '@/utils/running';
import {Location} from '@/types/running';

const RunningScreen: React.FC = () => {
  const {state, updateLocation} = useRunning();
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  const [watchId, setWatchId] = useState<number | null>(null);

  useEffect(() => {
    requestLocationPermission();
    return () => {
      if (watchId !== null) {
        Geolocation.clearWatch(watchId);
      }
    };
  }, []);

  // Start/stop location tracking based on running state
  useEffect(() => {
    if (state.isTracking && !state.isPaused && hasLocationPermission) {
      startLocationTracking();
    } else {
      stopLocationTracking();
    }

    return () => stopLocationTracking();
  }, [state.isTracking, state.isPaused, hasLocationPermission]);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background' && state.isTracking) {
        // Keep tracking in background
        console.log('App moved to background, continuing location tracking');
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => subscription?.remove();
  }, [state.isTracking]);

  const requestLocationPermission = async () => {
    try {
      const permission = Platform.select({
        ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
        android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      });

      if (!permission) {
        return;
      }

      const result = await request(permission);

      if (result === RESULTS.GRANTED) {
        setHasLocationPermission(true);
      } else {
        Alert.alert(
          'Location Permission Required',
          'This app needs location access to track your runs. Please enable location permissions in settings.',
          [
            {text: 'Cancel', style: 'cancel'},
            {
              text: 'Settings',
              onPress: () => {
                /* Open settings */
              },
            },
          ],
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
    }
  };

  const startLocationTracking = () => {
    if (watchId !== null) {
      Geolocation.clearWatch(watchId);
    }

    const id = Geolocation.watchPosition(
      position => {
        const location: Location = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          timestamp: position.timestamp,
          accuracy: position.coords.accuracy,
          altitude: position.coords.altitude || undefined,
          speed: position.coords.speed || undefined,
        };
        updateLocation(location);
      },
      error => {
        console.error('Location error:', error);
        Alert.alert(
          'Location Error',
          'Unable to get your location. Please check your GPS settings.',
        );
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 5, // Update every 5 meters
        interval: 1000, // Update every second
        fastestInterval: 500,
      },
    );

    setWatchId(id);
  };

  const stopLocationTracking = () => {
    if (watchId !== null) {
      Geolocation.clearWatch(watchId);
      setWatchId(null);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Map Container */}
      <View className="flex-1">
        <RunningMap />

        {/* Minimal Top Status */}
        <View className="absolute top-4 left-4 right-4 z-10">
          <View className="bg-white/90 backdrop-blur-sm rounded-2xl px-4 py-3 shadow-sm flex-row justify-between items-center">
            <Text className="text-gray-900 text-lg font-semibold">
              {state.isTracking ? 'Running' : 'Ready to Start'}
            </Text>
            <View
              className={`px-3 py-1 rounded-full ${
                state.isTracking
                  ? state.isPaused
                    ? 'bg-orange-100'
                    : 'bg-green-100'
                  : 'bg-blue-100'
              }`}>
              <Text
                className={`text-sm font-medium ${
                  state.isTracking
                    ? state.isPaused
                      ? 'text-orange-600'
                      : 'text-green-600'
                    : 'text-blue-600'
                }`}>
                {state.isTracking
                  ? state.isPaused
                    ? 'Paused'
                    : 'Active'
                  : 'Ready'}
              </Text>
            </View>
          </View>
        </View>

        {/* Live Stats Overlay */}
        {state.currentSession && (
          <View className="absolute bottom-4 left-4 right-4 z-10">
            <View className="bg-white/95 backdrop-blur-sm rounded-2xl p-4 shadow-lg">
              <View className="flex-row justify-between items-center">
                <View className="items-center flex-1">
                  <Text className="text-2xl font-bold text-blue-600">
                    {formatDistance(state.currentSession.distance)}
                  </Text>
                  <Text className="text-gray-500 text-xs font-medium">
                    Distance
                  </Text>
                </View>
                <View className="items-center flex-1">
                  <RunningTimer />
                </View>
                <View className="items-center flex-1">
                  <Text className="text-2xl font-bold text-orange-600">
                    {state.currentSession.calories}
                  </Text>
                  <Text className="text-gray-500 text-xs font-medium">
                    kcal
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}
      </View>

      {/* Bottom Sheet */}
      <View className="bg-white rounded-t-3xl shadow-2xl">
        {/* Handle */}
        <View className="w-12 h-1 bg-gray-300 rounded-full mx-auto mt-3 mb-4" />

        <View className="px-6 pb-8">
          {/* Main Controls */}
          <RunningControls className="mb-6" />

          {/* Additional Stats */}
          {state.currentSession && (
            <View className="bg-gray-50 rounded-2xl p-4">
              <Text className="text-gray-700 font-semibold mb-3">
                Session Stats
              </Text>
              <View className="flex-row justify-between">
                <View className="items-center">
                  <Text className="text-lg font-bold text-green-600">
                    {formatPace(state.currentSession.averagePace)}
                  </Text>
                  <Text className="text-gray-500 text-xs">Avg Pace</Text>
                </View>
                <View className="items-center">
                  <Text className="text-lg font-bold text-purple-600">
                    {(state.currentSession.averageSpeed * 3.6).toFixed(1)} km/h
                  </Text>
                  <Text className="text-gray-500 text-xs">Avg Speed</Text>
                </View>
                <View className="items-center">
                  <Text className="text-lg font-bold text-indigo-600">
                    {state.currentSession.route.length}
                  </Text>
                  <Text className="text-gray-500 text-xs">GPS Points</Text>
                </View>
              </View>
            </View>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
};

export default RunningScreen;
