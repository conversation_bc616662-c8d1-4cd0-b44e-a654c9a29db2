import React, {useEffect, useState} from 'react';
import {
  View,
  SafeAreaView,
  Alert,
  AppState,
  Platform,
  Text,
  TouchableOpacity,
} from 'react-native';
import {request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import Geolocation from '@react-native-community/geolocation';
import {useRunning} from '@/contexts/RunningContext';
import RunningControls from '@/components/running/RunningControls';
import RunningMap from '@/components/running/RunningMap';
import RunningTimer from '@/components/running/RunningTimer';
import QuickStats from '@/components/running/QuickStats';
import {formatDistance, formatPace} from '@/utils/running';
import {Location} from '@/types/running';
import Icon from 'react-native-vector-icons/Ionicons';

const RunningScreen: React.FC = () => {
  const {state, updateLocation} = useRunning();
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  const [watchId, setWatchId] = useState<number | null>(null);

  useEffect(() => {
    requestLocationPermission();
    return () => {
      if (watchId !== null) {
        Geolocation.clearWatch(watchId);
      }
    };
  }, []);

  // Start/stop location tracking based on running state
  useEffect(() => {
    if (state.isTracking && !state.isPaused && hasLocationPermission) {
      startLocationTracking();
    } else {
      stopLocationTracking();
    }

    return () => stopLocationTracking();
  }, [state.isTracking, state.isPaused, hasLocationPermission]);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background' && state.isTracking) {
        // Keep tracking in background
        console.log('App moved to background, continuing location tracking');
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => subscription?.remove();
  }, [state.isTracking]);

  const requestLocationPermission = async () => {
    try {
      const permission = Platform.select({
        ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
        android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      });

      if (!permission) {
        return;
      }

      const result = await request(permission);

      if (result === RESULTS.GRANTED) {
        setHasLocationPermission(true);
      } else {
        Alert.alert(
          'Location Permission Required',
          'This app needs location access to track your runs. Please enable location permissions in settings.',
          [
            {text: 'Cancel', style: 'cancel'},
            {
              text: 'Settings',
              onPress: () => {
                /* Open settings */
              },
            },
          ],
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
    }
  };

  const startLocationTracking = () => {
    if (watchId !== null) {
      Geolocation.clearWatch(watchId);
    }

    const id = Geolocation.watchPosition(
      position => {
        const location: Location = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          timestamp: position.timestamp,
          accuracy: position.coords.accuracy,
          altitude: position.coords.altitude || undefined,
          speed: position.coords.speed || undefined,
        };
        updateLocation(location);
      },
      error => {
        console.error('Location error:', error);
        Alert.alert(
          'Location Error',
          'Unable to get your location. Please check your GPS settings.',
        );
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 5, // Update every 5 meters
        interval: 1000, // Update every second
        fastestInterval: 500,
      },
    );

    setWatchId(id);
  };

  const stopLocationTracking = () => {
    if (watchId !== null) {
      Geolocation.clearWatch(watchId);
      setWatchId(null);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Map Section */}
      <View className="flex-1 relative">
        <RunningMap />

        {/* Header Overlay */}
        <View className="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-white via-white/80 to-transparent pt-12 pb-8">
          <View className="px-6">
            <View className="flex-row justify-between items-center mb-4">
              <View>
                <Text className="text-gray-900 text-2xl font-bold tracking-tight">
                  Running Session
                </Text>
                <Text className="text-gray-600 text-base font-medium mt-1">
                  {state.isTracking ? 'Session in Progress' : 'Ready to start'}
                </Text>
              </View>
              <View
                className={`rounded-2xl px-4 py-2 ${
                  state.isTracking
                    ? state.isPaused
                      ? 'bg-orange-100'
                      : 'bg-green-100'
                    : 'bg-blue-100'
                }`}>
                <Text
                  className={`text-sm font-semibold ${
                    state.isTracking
                      ? state.isPaused
                        ? 'text-orange-600'
                        : 'text-green-600'
                      : 'text-blue-600'
                  }`}>
                  {state.isTracking
                    ? state.isPaused
                      ? '⏸️ PAUSED'
                      : '🏃‍♂️ ACTIVE'
                    : '⚡ READY'}
                </Text>
              </View>
            </View>

            {/* Ready to Run Card */}
            {!state.isTracking && (
              <View className="bg-white rounded-3xl p-6 shadow-lg border border-gray-100">
                <View className="items-center">
                  <View className="bg-primary-50 rounded-full p-4 mb-4">
                    <Icon color="#4F46E5" name="location" size={32} />
                  </View>
                  <Text className="text-gray-900 text-xl font-bold mb-2">
                    Ready to Run
                  </Text>
                  <Text className="text-gray-600 text-center text-base">
                    Start your running session to see your route tracked on the
                    map in real-time
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Floating Stats Card */}
        {state.currentSession && (
          <View className="absolute top-36 left-6 right-6 z-10">
            <View className="bg-white rounded-3xl p-6 shadow-lg border border-gray-100">
              <RunningTimer className="mb-6" />
              <View className="flex-row justify-between">
                <View className="items-center flex-1">
                  <Text className="text-3xl font-bold text-primary-600">
                    {formatDistance(state.currentSession.distance)}
                  </Text>
                  <Text className="text-gray-600 text-sm font-semibold mt-1">
                    Distance
                  </Text>
                </View>
                <View className="items-center flex-1">
                  <Text className="text-3xl font-bold text-success-DEFAULT">
                    {formatPace(state.currentSession.averagePace)}
                  </Text>
                  <Text className="text-gray-600 text-sm font-semibold mt-1">
                    Pace
                  </Text>
                </View>
                <View className="items-center flex-1">
                  <Text className="text-3xl font-bold text-secondary-500">
                    {state.currentSession.calories}
                  </Text>
                  <Text className="text-gray-600 text-sm font-semibold mt-1">
                    kcal
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}
      </View>

      {/* Bottom Controls Panel */}
      <View className="bg-white border-t border-gray-100">
        <View className="px-6 pt-6 pb-8">
          {/* Quick Stats */}
          {state.currentSession && (
            <View className="mb-8">
              <QuickStats className="mb-8" session={state.currentSession} />
            </View>
          )}

          {/* Running Controls */}
          <View className="bg-gray-50 rounded-3xl p-6 border border-gray-100">
            <RunningControls />
          </View>

          {/* Safety Notice */}
          <View className="mt-6 pt-4 border-t border-gray-100">
            <Text className="text-gray-500 text-sm text-center leading-relaxed">
              Stay safe while running. Be aware of your surroundings and follow
              traffic rules.
            </Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default RunningScreen;
