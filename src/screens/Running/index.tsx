import React, {useEffect, useState} from 'react';
import {View, SafeAreaView, Alert, AppState, Platform} from 'react-native';
import {request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import Geolocation from '@react-native-community/geolocation';
import {useRunning} from '@/contexts/RunningContext';
import RunningStats from '@/components/running/RunningStats';
import RunningControls from '@/components/running/RunningControls';
import RunningMap from '@/components/running/RunningMap';
import {Location} from '@/types/running';

const RunningScreen: React.FC = () => {
  const {state, updateLocation} = useRunning();
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  const [watchId, setWatchId] = useState<number | null>(null);

  useEffect(() => {
    requestLocationPermission();
    return () => {
      if (watchId !== null) {
        Geolocation.clearWatch(watchId);
      }
    };
  }, []);

  // Start/stop location tracking based on running state
  useEffect(() => {
    if (state.isTracking && !state.isPaused && hasLocationPermission) {
      startLocationTracking();
    } else {
      stopLocationTracking();
    }

    return () => stopLocationTracking();
  }, [state.isTracking, state.isPaused, hasLocationPermission]);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background' && state.isTracking) {
        // Keep tracking in background
        console.log('App moved to background, continuing location tracking');
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => subscription?.remove();
  }, [state.isTracking]);

  const requestLocationPermission = async () => {
    try {
      const permission = Platform.select({
        ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
        android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      });

      if (!permission) return;

      const result = await request(permission);

      if (result === RESULTS.GRANTED) {
        setHasLocationPermission(true);
      } else {
        Alert.alert(
          'Location Permission Required',
          'This app needs location access to track your runs. Please enable location permissions in settings.',
          [
            {text: 'Cancel', style: 'cancel'},
            {
              text: 'Settings',
              onPress: () => {
                /* Open settings */
              },
            },
          ],
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
    }
  };

  const startLocationTracking = () => {
    if (watchId !== null) {
      Geolocation.clearWatch(watchId);
    }

    const id = Geolocation.watchPosition(
      position => {
        const location: Location = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          timestamp: position.timestamp,
          accuracy: position.coords.accuracy,
          altitude: position.coords.altitude || undefined,
          speed: position.coords.speed || undefined,
        };
        updateLocation(location);
      },
      error => {
        console.error('Location error:', error);
        Alert.alert(
          'Location Error',
          'Unable to get your location. Please check your GPS settings.',
        );
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 5, // Update every 5 meters
        interval: 1000, // Update every second
        fastestInterval: 500,
      },
    );

    setWatchId(id);
  };

  const stopLocationTracking = () => {
    if (watchId !== null) {
      Geolocation.clearWatch(watchId);
      setWatchId(null);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Map Section */}
      <View className="flex-1">
        <RunningMap />
      </View>

      {/* Stats and Controls Section */}
      <View className="bg-white px-6 py-6 shadow-lg rounded-t-3xl">
        {/* Running Stats */}
        <RunningStats session={state.currentSession} className="mb-6" />

        {/* Running Controls */}
        <RunningControls />
      </View>
    </SafeAreaView>
  );
};

export default RunningScreen;
