import React from 'react';
import {
  View,
  Text,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import {useRunning} from '@/contexts/RunningContext';
import {formatDistance, formatDuration, formatPace} from '@/utils/running';
import AchievementCard from '@/components/running/AchievementCard';

const Dashboard: React.FC = () => {
  const navigation = useNavigation();
  const {state} = useRunning();

  const quickStats = [
    {
      title: 'Total Runs',
      value: state.stats.totalRuns.toString(),
      iconName: 'footsteps',
      color: 'bg-primary-500',
      textColor: 'text-primary-600',
    },
    {
      title: 'Total Distance',
      value: formatDistance(state.stats.totalDistance),
      iconName: 'location',
      color: 'bg-success-DEFAULT',
      textColor: 'text-success-DEFAULT',
    },
    {
      title: 'Total Time',
      value: formatDuration(state.stats.totalDuration),
      iconName: 'time-outline',
      color: 'bg-accent-500',
      textColor: 'text-accent-600',
    },
    {
      title: 'Best Pace',
      value: formatPace(state.stats.bestPace),
      iconName: 'flash-outline',
      color: 'bg-secondary-500',
      textColor: 'text-secondary-600',
    },
  ];

  const recentRuns = state.runHistory.slice(0, 3);

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="bg-white px-6 py-8 shadow-card">
          <Text className="text-3xl font-bold text-gray-900 mb-2">
            Running Dashboard
          </Text>
          <Text className="text-gray-600 text-base">
            Track your progress and achieve your goals
          </Text>
        </View>

        {/* Quick Stats Grid */}
        <View className="px-6 py-6">
          <Text className="text-xl font-bold text-gray-900 mb-4">
            Your Stats
          </Text>
          <View className="flex-row flex-wrap -mx-2">
            {quickStats.map((stat, index) => (
              <View className="w-1/2 px-2 mb-4" key={index}>
                <View className="bg-white rounded-2xl p-5 shadow-stats backdrop-blur-lg border border-gray-100/20">
                  <View
                    className={`${stat.color} w-14 h-14 rounded-2xl items-center justify-center mb-4 shadow-sm`}>
                    <Icon color="white" name={stat.iconName} size={26} />
                  </View>
                  <Text className={`text-2xl font-bold ${stat.textColor} mb-1`}>
                    {stat.value}
                  </Text>
                  <Text className="text-gray-600 text-sm font-medium">
                    {stat.title}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Start Run Button */}
        <View className="px-6 mb-6">
          <TouchableOpacity
            className="bg-primary-600 rounded-3xl py-6 px-8 shadow-xl active:scale-98"
            onPress={() => navigation.navigate('Running' as never)}>
            <View className="items-center">
              <View className="bg-white/20 rounded-full p-3 mb-4">
                <Icon color="white" name="play" size={48} />
              </View>
              <Text className="text-white text-2xl font-bold mb-2">
                Start New Run
              </Text>
              <Text className="text-white text-base opacity-90">
                Ready to hit the road?
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Recent Runs */}
        <View className="px-6 mb-6">
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-xl font-bold text-gray-900">Recent Runs</Text>
            <TouchableOpacity
              className="bg-primary-50 px-4 py-2 rounded-full"
              onPress={() => navigation.navigate('History' as never)}>
              <Text className="text-primary-600 font-semibold text-sm">
                View All
              </Text>
            </TouchableOpacity>
          </View>

          {recentRuns.length > 0 ? (
            recentRuns.map((run, index) => (
              <TouchableOpacity
                className="bg-white rounded-2xl p-5 mb-3 shadow-card backdrop-blur-lg border border-gray-100/20 active:scale-98"
                key={run.id}
                onPress={() => navigation.navigate('History' as never)}>
                <View className="flex-row justify-between items-start mb-4">
                  <View>
                    <Text className="text-lg font-bold text-gray-900 mb-1">
                      Run #{state.stats.totalRuns - index}
                    </Text>
                    <Text className="text-gray-500 text-sm">
                      {new Date(run.startTime).toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric',
                      })}
                    </Text>
                  </View>
                  <View className="bg-success-DEFAULT/10 px-3 py-1 rounded-full">
                    <Text className="text-success-DEFAULT text-xs font-semibold">
                      Completed
                    </Text>
                  </View>
                </View>

                <View className="flex-row justify-between">
                  <View className="items-center flex-1">
                    <Text className="text-primary-600 font-bold text-lg">
                      {formatDistance(run.distance)}
                    </Text>
                    <Text className="text-gray-500 text-xs font-medium">
                      Distance
                    </Text>
                  </View>

                  <View className="items-center flex-1">
                    <Text className="text-success-DEFAULT font-bold text-lg">
                      {formatDuration(run.duration)}
                    </Text>
                    <Text className="text-gray-500 text-xs font-medium">
                      Duration
                    </Text>
                  </View>

                  <View className="items-center flex-1">
                    <Text className="text-secondary-500 font-bold text-lg">
                      {formatPace(run.averagePace)}
                    </Text>
                    <Text className="text-gray-500 text-xs font-medium">
                      Pace
                    </Text>
                  </View>

                  <View className="items-center flex-1">
                    <Text className="text-error-DEFAULT font-bold text-lg">
                      {run.calories}
                    </Text>
                    <Text className="text-gray-500 text-xs font-medium">
                      kcal
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))
          ) : (
            <View className="bg-white rounded-2xl p-8 items-center shadow-card backdrop-blur-lg border border-gray-100/20">
              <View className="mb-4">
                <Icon color="#9CA3AF" name="footsteps-outline" size={64} />
              </View>
              <Text className="text-gray-600 text-center text-base">
                No runs yet. Start your first run to see your progress here!
              </Text>
            </View>
          )}
        </View>

        {/* Achievements Section */}
        <View className="px-6 pb-6">
          <AchievementCard stats={state.stats} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Dashboard;
