import React from 'react';
import {
  View,
  Text,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useRunning} from '@/contexts/RunningContext';
import {formatDistance, formatDuration, formatPace} from '@/utils/running';
import AchievementCard from '@/components/running/AchievementCard';

const Dashboard: React.FC = () => {
  const navigation = useNavigation();
  const {state} = useRunning();

  const quickStats = [
    {
      title: 'Total Runs',
      value: state.stats.totalRuns.toString(),
      icon: '🏃‍♂️',
      color: 'bg-blue-500',
    },
    {
      title: 'Total Distance',
      value: formatDistance(state.stats.totalDistance),
      icon: '📏',
      color: 'bg-green-500',
    },
    {
      title: 'Total Time',
      value: formatDuration(state.stats.totalDuration),
      icon: '⏱️',
      color: 'bg-purple-500',
    },
    {
      title: 'Best Pace',
      value: formatPace(state.stats.bestPace),
      icon: '⚡',
      color: 'bg-orange-500',
    },
  ];

  const recentRuns = state.runHistory.slice(0, 3);

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView className="flex-1 px-4 py-6">
        {/* Header */}
        <View className="mb-6">
          <Text className="text-3xl font-bold text-gray-800 mb-2">
            Running Dashboard
          </Text>
          <Text className="text-gray-600">
            Track your progress and achieve your goals
          </Text>
        </View>

        {/* Quick Stats Grid */}
        <View className="mb-6">
          <Text className="text-xl font-bold text-gray-800 mb-4">
            Your Stats
          </Text>
          <View className="flex-row flex-wrap -mx-2">
            {quickStats.map((stat, index) => (
              <View className="w-1/2 px-2 mb-4" key={index}>
                <View className="bg-white rounded-2xl p-4 shadow-lg">
                  <View
                    className={`${stat.color} w-12 h-12 rounded-xl items-center justify-center mb-3`}>
                    <Text className="text-2xl">{stat.icon}</Text>
                  </View>
                  <Text className="text-2xl font-bold text-gray-800 mb-1">
                    {stat.value}
                  </Text>
                  <Text className="text-gray-600 text-sm">{stat.title}</Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Start Run Button */}
        <TouchableOpacity
          onPress={() => navigation.navigate('Running' as never)}
          className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl p-6 mb-6 shadow-lg active:scale-95">
          <View className="items-center">
            <Text className="text-white text-2xl font-bold mb-2">
              Start New Run
            </Text>
            <Text className="text-blue-100">🏃‍♂️ Ready to hit the road?</Text>
          </View>
        </TouchableOpacity>

        {/* Recent Runs */}
        <View className="mb-6">
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-xl font-bold text-gray-800">Recent Runs</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('History' as never)}>
              <Text className="text-blue-600 font-medium">View All</Text>
            </TouchableOpacity>
          </View>

          {recentRuns.length > 0 ? (
            recentRuns.map((run, index) => (
              <View
                className="bg-white rounded-2xl p-4 mb-3 shadow-lg"
                key={run.id}>
                <View className="flex-row justify-between items-start mb-2">
                  <Text className="text-lg font-bold text-gray-800">
                    Run #{state.stats.totalRuns - index}
                  </Text>
                  <Text className="text-gray-500 text-sm">
                    {new Date(run.startTime).toLocaleDateString()}
                  </Text>
                </View>

                <View className="flex-row justify-between">
                  <View className="items-center">
                    <Text className="text-blue-600 font-bold">
                      {formatDistance(run.distance)}
                    </Text>
                    <Text className="text-gray-500 text-xs">Distance</Text>
                  </View>

                  <View className="items-center">
                    <Text className="text-green-600 font-bold">
                      {formatDuration(run.duration)}
                    </Text>
                    <Text className="text-gray-500 text-xs">Duration</Text>
                  </View>

                  <View className="items-center">
                    <Text className="text-orange-600 font-bold">
                      {formatPace(run.averagePace)}
                    </Text>
                    <Text className="text-gray-500 text-xs">Pace</Text>
                  </View>

                  <View className="items-center">
                    <Text className="text-red-600 font-bold">
                      {run.calories}
                    </Text>
                    <Text className="text-gray-500 text-xs">Calories</Text>
                  </View>
                </View>
              </View>
            ))
          ) : (
            <View className="bg-white rounded-2xl p-8 items-center shadow-lg">
              <Text className="text-6xl mb-4">🏃‍♂️</Text>
              <Text className="text-gray-600 text-center">
                No runs yet. Start your first run to see your progress here!
              </Text>
            </View>
          )}
        </View>

        {/* Achievements Section */}
        <AchievementCard stats={state.stats} className="mb-6" />
      </ScrollView>
    </SafeAreaView>
  );
};

export default Dashboard;
