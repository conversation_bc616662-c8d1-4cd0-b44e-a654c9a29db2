import {Home} from '@/screens';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import React from 'react';
import {navigationRef} from './NavigatorService';

const Navigator = () => {
  const Stack = createNativeStackNavigator();

  const SCREENS: Record<string, React.ComponentType> = {
    Home,
  };

  const renderListChild = () => (
    <>
      {Object.entries(SCREENS).map(([name, component]) => (
        <Stack.Screen
          component={component}
          key={name}
          name={name}
          options={() => ({
            headerShown: false,
          })}
        />
      ))}
    </>
  );

  return (
    <NavigationContainer ref={navigationRef}>
      <Stack.Navigator>{renderListChild()}</Stack.Navigator>
    </NavigationContainer>
  );
};

export default Navigator;
