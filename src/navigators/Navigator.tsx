import {Home} from '@/screens';
import Dashboard from '@/screens/Dashboard';
import RunningScreen from '@/screens/Running';
import History from '@/screens/History';
import {NavigationContainer} from '@react-navigation/native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import React from 'react';
import {Text} from 'react-native';
import {navigationRef} from './NavigatorService';
import {RunningProvider} from '@/contexts/RunningContext';

const Navigator = () => {
  const Tab = createBottomTabNavigator();

  return (
    <RunningProvider>
      <NavigationContainer ref={navigationRef}>
        <Tab.Navigator
          screenOptions={{
            headerShown: false,
            tabBarStyle: {
              backgroundColor: 'white',
              borderTopWidth: 0,
              elevation: 8,
              shadowOpacity: 0.1,
              shadowRadius: 4,
              shadowOffset: {width: 0, height: -2},
              height: 90,
              paddingBottom: 20,
              paddingTop: 10,
            },
            tabBarActiveTintColor: '#3B82F6',
            tabBarInactiveTintColor: '#9CA3AF',
            tabBarLabelStyle: {
              fontSize: 12,
              fontWeight: '600',
              marginTop: 4,
            },
          }}>
          <Tab.Screen
            name="Dashboard"
            component={Dashboard}
            options={{
              tabBarIcon: ({color}) => (
                <Text style={{fontSize: 24, color}}>📊</Text>
              ),
              tabBarLabel: 'Dashboard',
            }}
          />

          <Tab.Screen
            name="Running"
            component={RunningScreen}
            options={{
              tabBarIcon: ({color}) => (
                <Text style={{fontSize: 24, color}}>🏃‍♂️</Text>
              ),
              tabBarLabel: 'Run',
            }}
          />

          <Tab.Screen
            name="History"
            component={History}
            options={{
              tabBarIcon: ({color}) => (
                <Text style={{fontSize: 24, color}}>📊</Text>
              ),
              tabBarLabel: 'History',
            }}
          />

          <Tab.Screen
            name="Map"
            component={Home}
            options={{
              tabBarIcon: ({color}) => (
                <Text style={{fontSize: 24, color}}>🗺️</Text>
              ),
              tabBarLabel: 'Map',
            }}
          />
        </Tab.Navigator>
      </NavigationContainer>
    </RunningProvider>
  );
};

export default Navigator;
