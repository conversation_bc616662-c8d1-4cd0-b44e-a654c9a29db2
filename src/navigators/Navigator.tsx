import {Home} from '@/screens';
import Dashboard from '@/screens/Dashboard';
import RunningScreen from '@/screens/Running';
import History from '@/screens/History';
import {NavigationContainer} from '@react-navigation/native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import React from 'react';
import Icon from 'react-native-vector-icons/Ionicons';
import {navigationRef} from './NavigatorService';
import {RunningProvider} from '@/contexts/RunningContext';

const Navigator = () => {
  const Tab = createBottomTabNavigator();

  return (
    <RunningProvider>
      <NavigationContainer ref={navigationRef}>
        <Tab.Navigator
          screenOptions={{
            headerShown: false,
            tabBarStyle: {
              backgroundColor: 'white',
              borderTopWidth: 0,
              elevation: 8,
              shadowOpacity: 0.1,
              shadowRadius: 4,
              shadowOffset: {width: 0, height: -2},
              height: 90,
              paddingBottom: 20,
              paddingTop: 10,
            },
            tabBarActiveTintColor: '#3B82F6',
            tabBarInactiveTintColor: '#9CA3AF',
            tabBarLabelStyle: {
              fontSize: 12,
              fontWeight: '600',
              marginTop: 4,
            },
          }}>
          <Tab.Screen
            component={Dashboard}
            name="Dashboard"
            options={{
              tabBarIcon: ({color, size = 24}) => (
                <Icon color={color} name="stats-chart" size={size} />
              ),
              tabBarLabel: 'Dashboard',
            }}
          />

          <Tab.Screen
            component={RunningScreen}
            name="Running"
            options={{
              tabBarIcon: ({color, size = 24}) => (
                <Icon color={color} name="play-circle" size={size} />
              ),
              tabBarLabel: 'Run',
            }}
          />

          <Tab.Screen
            component={History}
            name="History"
            options={{
              tabBarIcon: ({color, size = 24}) => (
                <Icon color={color} name="time" size={size} />
              ),
              tabBarLabel: 'History',
            }}
          />

          <Tab.Screen
            component={Home}
            name="Map"
            options={{
              tabBarIcon: ({color, size = 24}) => (
                <Icon color={color} name="map" size={size} />
              ),
              tabBarLabel: 'Map',
            }}
          />
        </Tab.Navigator>
      </NavigationContainer>
    </RunningProvider>
  );
};

export default Navigator;
