export interface Location {
  latitude: number;
  longitude: number;
  timestamp: number;
  accuracy?: number;
  altitude?: number;
  speed?: number;
}

export interface RunSession {
  id: string;
  startTime: number;
  endTime?: number;
  duration: number; // in seconds
  distance: number; // in meters
  averageSpeed: number; // in m/s
  maxSpeed: number; // in m/s
  averagePace: number; // in seconds per km
  calories: number;
  route: Location[];
  status: 'active' | 'paused' | 'completed';
  name?: string;
  notes?: string;
}

export interface RunStats {
  totalRuns: number;
  totalDistance: number; // in meters
  totalDuration: number; // in seconds
  totalCalories: number;
  averagePace: number; // in seconds per km
  bestPace: number; // in seconds per km
  longestRun: number; // in meters
  longestDuration: number; // in seconds
}

export interface WeeklyStats {
  week: string; // YYYY-MM-DD format for Monday of the week
  runs: number;
  distance: number;
  duration: number;
  calories: number;
}

export interface RunningState {
  currentSession: RunSession | null;
  isTracking: boolean;
  isPaused: boolean;
  currentLocation: Location | null;
  runHistory: RunSession[];
  stats: RunStats;
  weeklyStats: WeeklyStats[];
}

// Utility types
export type RunningStatus = 'idle' | 'tracking' | 'paused';

export interface RunMetrics {
  distance: string;
  duration: string;
  pace: string;
  speed: string;
  calories: string;
}
