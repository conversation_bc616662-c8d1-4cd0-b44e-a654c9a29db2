import React, {createContext, useContext, useReducer, useEffect} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {RunningState, RunSession, Location, RunStats} from '@/types/running';
import {
  calculateRouteDistance,
  calculatePace,
  calculateSpeed,
  calculateCalories,
  generateRunId,
  isValidLocation,
} from '@/utils/running';

// Storage keys
const STORAGE_KEYS = {
  RUN_HISTORY: 'run_history',
  RUN_STATS: 'run_stats',
};

// Initial state
const initialState: RunningState = {
  currentSession: null,
  isTracking: false,
  isPaused: false,
  currentLocation: null,
  runHistory: [],
  stats: {
    totalRuns: 0,
    totalDistance: 0,
    totalDuration: 0,
    totalCalories: 0,
    averagePace: 0,
    bestPace: 0,
    longestRun: 0,
    longestDuration: 0,
  },
  weeklyStats: [],
};

// Action types
type RunningAction =
  | {type: 'START_RUN'}
  | {type: 'PAUSE_RUN'}
  | {type: 'RESUME_RUN'}
  | {type: 'STOP_RUN'}
  | {type: 'UPDATE_LOCATION'; payload: Location}
  | {type: 'LOAD_HISTORY'; payload: RunSession[]}
  | {type: 'LOAD_STATS'; payload: RunStats}
  | {type: 'SAVE_RUN'; payload: RunSession};

// Reducer
const runningReducer = (
  state: RunningState,
  action: RunningAction,
): RunningState => {
  switch (action.type) {
    case 'START_RUN':
      return {
        ...state,
        currentSession: {
          id: generateRunId(),
          startTime: Date.now(),
          duration: 0,
          distance: 0,
          averageSpeed: 0,
          maxSpeed: 0,
          averagePace: 0,
          calories: 0,
          route: [],
          status: 'active',
        },
        isTracking: true,
        isPaused: false,
      };

    case 'PAUSE_RUN':
      return {
        ...state,
        isPaused: true,
        currentSession: state.currentSession
          ? {...state.currentSession, status: 'paused'}
          : null,
      };

    case 'RESUME_RUN':
      return {
        ...state,
        isPaused: false,
        currentSession: state.currentSession
          ? {...state.currentSession, status: 'active'}
          : null,
      };

    case 'STOP_RUN':
      return {
        ...state,
        isTracking: false,
        isPaused: false,
        currentSession: null,
      };

    case 'UPDATE_LOCATION':
      if (!state.currentSession || !state.isTracking || state.isPaused) {
        return {...state, currentLocation: action.payload};
      }

      const newLocation = action.payload;
      if (!isValidLocation(newLocation)) {
        return state;
      }

      const updatedRoute = [...state.currentSession.route, newLocation];
      const distance = calculateRouteDistance(updatedRoute);
      const duration = (Date.now() - state.currentSession.startTime) / 1000;
      const averageSpeed = calculateSpeed(distance, duration);
      const maxSpeed = Math.max(
        state.currentSession.maxSpeed,
        newLocation.speed || 0,
      );
      const averagePace = calculatePace(distance, duration);
      const calories = calculateCalories(distance);

      return {
        ...state,
        currentLocation: newLocation,
        currentSession: {
          ...state.currentSession,
          route: updatedRoute,
          distance,
          duration,
          averageSpeed,
          maxSpeed,
          averagePace,
          calories,
        },
      };

    case 'LOAD_HISTORY':
      return {
        ...state,
        runHistory: action.payload,
      };

    case 'LOAD_STATS':
      return {
        ...state,
        stats: action.payload,
      };

    case 'SAVE_RUN':
      const newHistory = [action.payload, ...state.runHistory];
      return {
        ...state,
        runHistory: newHistory,
        currentSession: null,
        isTracking: false,
        isPaused: false,
      };

    default:
      return state;
  }
};

// Context
interface RunningContextType {
  state: RunningState;
  startRun: () => void;
  pauseRun: () => void;
  resumeRun: () => void;
  stopRun: () => Promise<void>;
  updateLocation: (location: Location) => void;
  saveRun: (session: RunSession) => Promise<void>;
  loadData: () => Promise<void>;
}

const RunningContext = createContext<RunningContextType | undefined>(undefined);

// Provider
export const RunningProvider: React.FC<{children: React.ReactNode}> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(runningReducer, initialState);

  // Load data on mount
  useEffect(() => {
    loadData();
  }, []);

  const startRun = () => {
    dispatch({type: 'START_RUN'});
  };

  const pauseRun = () => {
    dispatch({type: 'PAUSE_RUN'});
  };

  const resumeRun = () => {
    dispatch({type: 'RESUME_RUN'});
  };

  const stopRun = async () => {
    if (state.currentSession) {
      const completedSession: RunSession = {
        ...state.currentSession,
        endTime: Date.now(),
        status: 'completed',
      };
      await saveRun(completedSession);
    }
    dispatch({type: 'STOP_RUN'});
  };

  const updateLocation = (location: Location) => {
    dispatch({type: 'UPDATE_LOCATION', payload: location});
  };

  const saveRun = async (session: RunSession) => {
    try {
      const history = [...state.runHistory, session];
      await AsyncStorage.setItem(STORAGE_KEYS.RUN_HISTORY, JSON.stringify(history));
      
      // Update stats
      const newStats = calculateStats(history);
      await AsyncStorage.setItem(STORAGE_KEYS.RUN_STATS, JSON.stringify(newStats));
      
      dispatch({type: 'SAVE_RUN', payload: session});
      dispatch({type: 'LOAD_STATS', payload: newStats});
    } catch (error) {
      console.error('Error saving run:', error);
    }
  };

  const loadData = async () => {
    try {
      const [historyData, statsData] = await Promise.all([
        AsyncStorage.getItem(STORAGE_KEYS.RUN_HISTORY),
        AsyncStorage.getItem(STORAGE_KEYS.RUN_STATS),
      ]);

      if (historyData) {
        const history = JSON.parse(historyData);
        dispatch({type: 'LOAD_HISTORY', payload: history});
      }

      if (statsData) {
        const stats = JSON.parse(statsData);
        dispatch({type: 'LOAD_STATS', payload: stats});
      }
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  const calculateStats = (history: RunSession[]): RunStats => {
    if (history.length === 0) return initialState.stats;

    const totalRuns = history.length;
    const totalDistance = history.reduce((sum, run) => sum + run.distance, 0);
    const totalDuration = history.reduce((sum, run) => sum + run.duration, 0);
    const totalCalories = history.reduce((sum, run) => sum + run.calories, 0);
    const averagePace = totalDistance > 0 ? totalDuration / (totalDistance / 1000) : 0;
    const bestPace = Math.min(...history.map(run => run.averagePace).filter(pace => pace > 0));
    const longestRun = Math.max(...history.map(run => run.distance));
    const longestDuration = Math.max(...history.map(run => run.duration));

    return {
      totalRuns,
      totalDistance,
      totalDuration,
      totalCalories,
      averagePace,
      bestPace: isFinite(bestPace) ? bestPace : 0,
      longestRun,
      longestDuration,
    };
  };

  return (
    <RunningContext.Provider
      value={{
        state,
        startRun,
        pauseRun,
        resumeRun,
        stopRun,
        updateLocation,
        saveRun,
        loadData,
      }}>
      {children}
    </RunningContext.Provider>
  );
};

// Hook
export const useRunning = () => {
  const context = useContext(RunningContext);
  if (!context) {
    throw new Error('useRunning must be used within RunningProvider');
  }
  return context;
};
