import {Location, RunSession, RunMetrics} from '@/types/running';

/**
 * Calculate distance between two coordinates using Haversine formula
 */
export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
): number => {
  const R = 6371000; // Earth's radius in meters
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLon = (lon2 - lon1) * (Math.PI / 180);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) *
      Math.cos(lat2 * (Math.PI / 180)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

/**
 * Calculate total distance from route points
 */
export const calculateRouteDistance = (route: Location[]): number => {
  if (route.length < 2) return 0;

  let totalDistance = 0;
  for (let i = 1; i < route.length; i++) {
    const prev = route[i - 1];
    const curr = route[i];
    totalDistance += calculateDistance(
      prev.latitude,
      prev.longitude,
      curr.latitude,
      curr.longitude,
    );
  }
  return totalDistance;
};

/**
 * Calculate pace in seconds per kilometer
 */
export const calculatePace = (distance: number, duration: number): number => {
  if (distance === 0) return 0;
  const distanceInKm = distance / 1000;
  return duration / distanceInKm;
};

/**
 * Calculate speed in m/s
 */
export const calculateSpeed = (distance: number, duration: number): number => {
  if (duration === 0) return 0;
  return distance / duration;
};

/**
 * Estimate calories burned (rough calculation)
 * Based on: calories = weight(kg) * distance(km) * 1.036
 */
export const calculateCalories = (
  distance: number,
  weight: number = 70,
): number => {
  const distanceInKm = distance / 1000;
  return Math.round(weight * distanceInKm * 1.036);
};

/**
 * Format duration to HH:MM:SS
 */
export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes.toString().padStart(2, '0')}:${secs
    .toString()
    .padStart(2, '0')}`;
};

/**
 * Format distance to readable string
 */
export const formatDistance = (meters: number): string => {
  if (meters < 1000) {
    return `${Math.round(meters)}m`;
  }
  return `${(meters / 1000).toFixed(2)}km`;
};

/**
 * Format pace to MM:SS per km
 */
export const formatPace = (secondsPerKm: number): string => {
  if (secondsPerKm === 0 || !isFinite(secondsPerKm)) return '--:--';
  
  const minutes = Math.floor(secondsPerKm / 60);
  const seconds = Math.floor(secondsPerKm % 60);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

/**
 * Format speed to km/h
 */
export const formatSpeed = (metersPerSecond: number): string => {
  const kmh = (metersPerSecond * 3.6);
  return `${kmh.toFixed(1)} km/h`;
};

/**
 * Get formatted metrics for display
 */
export const getRunMetrics = (session: RunSession): RunMetrics => {
  return {
    distance: formatDistance(session.distance),
    duration: formatDuration(session.duration),
    pace: formatPace(session.averagePace),
    speed: formatSpeed(session.averageSpeed),
    calories: `${session.calories} kcal`,
  };
};

/**
 * Generate unique ID for run session
 */
export const generateRunId = (): string => {
  return `run_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Check if location is valid
 */
export const isValidLocation = (location: Location): boolean => {
  return (
    location.latitude >= -90 &&
    location.latitude <= 90 &&
    location.longitude >= -180 &&
    location.longitude <= 180 &&
    (location.accuracy === undefined || location.accuracy < 50) // 50m accuracy threshold
  );
};
