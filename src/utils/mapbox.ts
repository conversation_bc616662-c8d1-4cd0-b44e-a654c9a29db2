import Mapbox from '@rnmapbox/maps';

// Mapbox style URLs
export const MapStyles = {
  STREET: Mapbox.StyleURL.Street,
  SATELLITE: Mapbox.StyleURL.Satellite,
  SATELLITE_STREETS: Mapbox.StyleURL.SatelliteStreet,
  LIGHT: Mapbox.StyleURL.Light,
  DARK: Mapbox.StyleURL.Dark,
  OUTDOORS: Mapbox.StyleURL.Outdoors,
} as const;

// Common coordinates for Vietnam
export const VietnamCoordinates = {
  HO_CHI_MINH: [106.6297, 10.8231] as [number, number],
  HANOI: [105.8542, 21.0285] as [number, number],
  DA_NANG: [108.2022, 16.0544] as [number, number],
  CAN_THO: [105.7851, 10.0452] as [number, number],
  HAI_PHONG: [106.6881, 20.8449] as [number, number],
} as const;

// Utility functions
export const mapboxUtils = {
  /**
   * Calculate distance between two coordinates (in kilometers)
   */
  calculateDistance: (
    coord1: [number, number],
    coord2: [number, number],
  ): number => {
    const [lon1, lat1] = coord1;
    const [lon2, lat2] = coord2;

    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLon = (lon2 - lon1) * (Math.PI / 180);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * (Math.PI / 180)) *
        Math.cos(lat2 * (Math.PI / 180)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  },

  /**
   * Format coordinates for display
   */
  formatCoordinates: (coordinates: [number, number], precision = 6): string => {
    const [lng, lat] = coordinates;
    return `${lat.toFixed(precision)}, ${lng.toFixed(precision)}`;
  },

  /**
   * Check if coordinates are valid
   */
  isValidCoordinates: (coordinates: [number, number]): boolean => {
    const [lng, lat] = coordinates;
    return (
      lng >= -180 &&
      lng <= 180 &&
      lat >= -90 &&
      lat <= 90 &&
      !isNaN(lng) &&
      !isNaN(lat)
    );
  },

  /**
   * Get bounds for multiple coordinates
   */
  getBounds: (
    coordinates: [number, number][],
  ): {
    ne: [number, number];
    sw: [number, number];
  } => {
    if (coordinates.length === 0) {
      throw new Error('Coordinates array cannot be empty');
    }

    let minLng = coordinates[0][0];
    let maxLng = coordinates[0][0];
    let minLat = coordinates[0][1];
    let maxLat = coordinates[0][1];

    coordinates.forEach(([lng, lat]) => {
      minLng = Math.min(minLng, lng);
      maxLng = Math.max(maxLng, lng);
      minLat = Math.min(minLat, lat);
      maxLat = Math.max(maxLat, lat);
    });

    return {
      ne: [maxLng, maxLat],
      sw: [minLng, minLat],
    };
  },

  /**
   * Convert address to coordinates (requires geocoding service)
   */
  geocodeAddress: async (address: string): Promise<[number, number] | null> => {
    // This would require a geocoding service like Mapbox Geocoding API
    // For now, return null as placeholder
    console.warn('Geocoding not implemented. Use Mapbox Geocoding API.');
    return null;
  },
};
