# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

require_relative '../node_modules/react-native-permissions/scripts/setup'

platform :ios, min_ios_version_supported
prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

# highlight-start
pre_install do |installer|
  $RNMapboxMaps.pre_install(installer)
end
# highlight-end

target 'BaseRN' do
  config = use_native_modules!

  # Setup react-native-permissions
  setup_permissions([
    'LocationWhenInUse',
  ])

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  # highlight-start
  post_install do |installer|
    $RNMapboxMaps.post_install(installer)
  # highlight-end

    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
  end
end
