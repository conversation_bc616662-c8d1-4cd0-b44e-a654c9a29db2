# 🏃‍♂️ Running Tracker App

Ứng dụng theo dõi chạy bộ được xây dựng với React Native, Mapbox, và Tailwind CSS.

## ✨ Tính năng chính

### 🎯 Core Features

- **Real-time GPS Tracking**: <PERSON> dõi vị trí GPS chính xác
- **Live Route Mapping**: Hiển thị route chạy trên bản đồ Mapbox
- **Performance Metrics**: Tính toán distance, pace, speed, calories
- **Session Management**: Start, pause, resume, stop runs
- **Data Persistence**: <PERSON><PERSON>u trữ lịch sử chạy với AsyncStorage

### 📊 Dashboard

- **Quick Stats**: Tổng quan thống kê (total runs, distance, time, best pace)
- **Recent Runs**: Hiển thị 3 lần chạy gần nhất
- **Achievement System**: <PERSON><PERSON> thống thành tích với progress tracking
- **Navigation**: Quick access đến Running và History screens

### 🏃‍♂️ Running Screen

- **Live Timer**: <PERSON><PERSON><PERSON> hồ bấm giờ real-time với centiseconds
- **Live Stats**: Distance, pace, speed, calories được cập nhật liên tục
- **Interactive Map**: Mapbox map với route tracking và user location
- **Control Buttons**: Start, pause, resume, stop với UI đẹp
- **Status Indicators**: Visual feedback cho trạng thái running/paused

### 📈 History Screen

- **Run List**: Danh sách tất cả runs với mini route maps
- **Route Visualization**: Mini maps hiển thị route trong mỗi run card
- **Detailed View**: Chi tiết từng run với full-size route map
- **Route Analysis**: GPS-based distance, speed, elevation analysis
- **Performance Metrics**: Max speed, elevation gain/loss, GPS accuracy
- **Interactive Maps**: Full-featured Mapbox maps với start/end markers
- **Date/Time Info**: Thời gian start/end, ngày chạy

### 🗺️ Map Integration

- **Mapbox Integration**: High-quality maps với multiple styles
- **Route Visualization**: Blue line hiển thị đường chạy
- **Start/End Markers**: Visual markers cho điểm bắt đầu
- **User Location**: Real-time location puck với heading
- **Map Styles**: Street, Satellite, Dark, Light, Outdoors

## 🛠️ Tech Stack

### Frontend

- **React Native**: Cross-platform mobile development
- **TypeScript**: Type safety và better development experience
- **Tailwind CSS**: Utility-first CSS framework
- **React Navigation**: Tab navigation với beautiful UI

### Maps & Location

- **@rnmapbox/maps**: Mapbox SDK cho React Native
- **@react-native-community/geolocation**: GPS location tracking
- **react-native-permissions**: Location permissions management

### State Management

- **React Context**: Global state cho running data
- **useReducer**: Complex state logic management
- **AsyncStorage**: Local data persistence

### UI Components

- **Custom Components**: Reusable running-specific components
- **Route Maps**: Interactive Mapbox route visualization
- **Mini Route Maps**: Compact route previews in cards
- **Route Analysis**: GPS-based performance analytics
- **Gradient Buttons**: Beautiful gradient styling
- **Animated Timer**: Smooth real-time timer updates
- **Achievement Cards**: Gamification elements

## 📱 Screens Overview

### 1. Dashboard Tab 📊

- Welcome screen với overview stats
- Quick start running button
- Recent runs preview
- Achievement progress
- Navigation đến other screens

### 2. Running Tab 🏃‍♂️

- Main running interface
- Live map với route tracking
- Real-time metrics display
- Control buttons (start/pause/stop)
- Timer với centiseconds

### 3. History Tab 📈

- Complete run history với mini route maps
- Interactive route visualization
- Detailed run analysis với full-size maps
- GPS-based performance analytics
- Route statistics (distance, speed, elevation)
- Date/time filtering
- Run comparison

### 4. Map Tab 🗺️

- Pure map view (từ setup ban đầu)
- Map style testing
- Mapbox integration demo

## 🎨 UI/UX Design

### Design Principles

- **Clean & Modern**: Minimalist design với focus vào functionality
- **Consistent Colors**: Primary blue theme với accent colors
- **Readable Typography**: Clear fonts với proper hierarchy
- **Intuitive Navigation**: Easy-to-use tab navigation
- **Visual Feedback**: Status indicators và progress bars

### Color Scheme

- **Primary**: Blue (#3B82F6) - buttons, highlights
- **Success**: Green (#10B981) - running status, achievements
- **Warning**: Yellow (#F59E0B) - paused status
- **Danger**: Red (#EF4444) - stop button, calories
- **Gray Scale**: Various grays cho text và backgrounds

### Components

- **Rounded Corners**: 2xl radius cho modern look
- **Shadows**: Subtle shadows cho depth
- **Gradients**: Linear gradients cho premium feel
- **Icons**: Emoji icons cho fun và recognition
- **Grid Layouts**: Responsive grid cho stats display

## 🚀 Performance Features

### GPS Optimization

- **High Accuracy**: enableHighAccuracy: true
- **Distance Filter**: Update every 5 meters
- **Time Interval**: 1 second updates
- **Background Tracking**: Continues khi app ở background

### Calculations

- **Haversine Formula**: Accurate distance calculation
- **Real-time Metrics**: Live pace, speed calculations
- **Calorie Estimation**: Weight-based calorie calculation
- **Performance Stats**: Max speed, average pace tracking

### Data Management

- **Efficient Storage**: JSON serialization cho AsyncStorage
- **Memory Management**: Proper cleanup của intervals
- **State Optimization**: Minimal re-renders với proper dependencies

## 🎯 Achievement System

### Available Achievements

1. **First Steps** 🏃‍♂️ - Complete first run
2. **Getting Started** 🎯 - Complete 5 runs
3. **5K Runner** 🏅 - Run 5km in single session
4. **Time Warrior** ⏰ - Accumulate 10 hours
5. **Century Runner** 💯 - Run total 100km
6. **Consistent Runner** 🔥 - Complete 30 runs

### Progress Tracking

- Visual progress bars
- Percentage completion
- Next goal highlighting
- Unlocked achievement display

## 📊 Metrics Calculated

### Distance

- Real-time GPS-based calculation
- Haversine formula cho accuracy
- Display trong km/m format

### Pace

- Minutes per kilometer
- Real-time calculation
- Average pace tracking

### Speed

- Current speed từ GPS
- Average speed calculation
- Max speed recording
- Display trong km/h

### Calories

- Weight-based estimation
- Distance-dependent calculation
- Real-time updates

### Time

- Precise timing với centiseconds
- Pause/resume functionality
- Total duration tracking

## 🔧 Setup Instructions

1. **Install Dependencies**

   ```bash
   npm install
   cd ios && pod install
   ```

2. **Configure Mapbox Token**

   - Update `.env` file với real Mapbox token
   - Get token từ https://account.mapbox.com/

3. **Run App**
   ```bash
   npx react-native run-ios
   # or
   npx react-native run-android
   ```

## 🎉 Kết quả

Ứng dụng running tracker hoàn chỉnh với:

- ✅ Beautiful, modern UI với Tailwind CSS
- ✅ Real-time GPS tracking và mapping
- ✅ Comprehensive performance metrics
- ✅ Achievement system cho motivation
- ✅ Complete run history management
- ✅ Smooth animations và interactions
- ✅ Cross-platform compatibility

Perfect cho việc tracking running sessions với professional-grade features!
