{"name": "BaseRN", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "pod": "cd ios && pod install && cd .."}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/geolocation": "^3.4.0", "@react-navigation/bottom-tabs": "^7.2.1", "@react-navigation/native": "^7.0.15", "@react-navigation/native-stack": "^7.2.1", "@rnmapbox/maps": "^10.1.39", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.78.0", "react-native-config": "^1.5.5", "react-native-permissions": "^5.4.0", "react-native-reanimated": "^3.17.1", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.9.1", "react-native-svg": "^7.2.1", "react-native-svg-charts": "^5.4.0", "react-native-vector-icons": "^10.2.0", "tailwindcss": "^3.4.17", "victory-native": "^41.17.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.0", "@react-native/eslint-config": "0.78.0", "@react-native/metro-config": "0.78.0", "@react-native/typescript-config": "0.78.0", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}