/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./App.{js,jsx,ts,tsx}', './src/**/*.{js,jsx,ts,tsx}'],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#1E88E5', // <PERSON><PERSON><PERSON> (blue)
          dark: '#1565C0', // Biến thể đậm
          light: '#42A5F5', // <PERSON>iến thể nhạt
        },
        secondary: {
          DEFAULT: '#FF9800', // <PERSON><PERSON><PERSON> phụ (orange)
          dark: '#EF6C00',
          light: '#FFB74D',
        },
        accent: {
          DEFAULT: '#9C27B0', // M<PERSON>u nhấn (purple)
          dark: '#7B1FA2',
          light: '#CE93D8',
        },
        success: '#4CAF50', // <PERSON><PERSON><PERSON> thành công (green)
        warning: '#FFC107', // <PERSON><PERSON><PERSON> cảnh báo (yellow)
        danger: '#F44336', // <PERSON><PERSON><PERSON> lỗi (red)
        background: '#F5F5F5', // <PERSON><PERSON><PERSON> nền mặc định
        text: '#212121', // Màu chữ chính
      },
      fontFamily: {
        sans: ['BeVietnamPro-Regular', 'sans-serif'],
        bold: ['BeVietnamPro-Bold', 'sans-serif'],
        semibold: ['BeVietnamPro-SemiBold', 'sans-serif'],
        medium: ['BeVietnamPro-Medium', 'sans-serif'],
        thin: ['BeVietnamPro-Thin', 'sans-serif'],
        mediumItalic: ['BeVietnamPro-MediumItalic', 'sans-serif'],
      },
      fontWeight: {
        thin: '100',
        light: '300',
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      },
    },
  },
  plugins: [],
};
